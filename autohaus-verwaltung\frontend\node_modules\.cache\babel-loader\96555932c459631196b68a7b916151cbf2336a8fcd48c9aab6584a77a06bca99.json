{"ast": null, "code": "// Fahrzeug-Service für lokale Datenverwaltung\n\n// Mock-Daten für Autohaus-Fahrzeuge\nconst mockVehicles = [\n// Beispiel-Fahrzeuge für dein Autohaus\n{\n  id: 'vh-001',\n  make: 'BMW',\n  model: '320i',\n  year: 2023,\n  price: 45000,\n  status: 'verfügbar',\n  type: 'Limousine',\n  fuel: 'Benzin',\n  km: 12500,\n  color: 'Schwarz',\n  vin: 'WBA123456789',\n  location: 'Stellplatz A1'\n}, {\n  id: 'vh-002',\n  make: 'Mercedes',\n  model: 'C-Klasse',\n  year: 2022,\n  price: 52000,\n  status: 'verkauft',\n  type: 'Limousine',\n  fuel: 'Diesel',\n  km: 25000,\n  color: 'Silber',\n  vin: 'WDD987654321',\n  location: 'Stellplatz A2'\n}, {\n  id: 'vh-003',\n  make: 'Audi',\n  model: 'A4',\n  year: 2023,\n  price: 48000,\n  status: 'reserviert',\n  type: 'Limousine',\n  fuel: 'Benzin',\n  km: 8500,\n  color: 'Weiß',\n  vin: 'WAU456789123',\n  location: 'Stellplatz B1'\n}, {\n  id: 'vh-004',\n  make: 'Volkswagen',\n  model: 'Golf',\n  year: 2022,\n  price: 28000,\n  status: 'verfügbar',\n  type: 'Kompakt',\n  fuel: 'Benzin',\n  km: 18000,\n  color: 'Blau',\n  vin: 'WVW789123456',\n  location: 'Stellplatz B2'\n}, {\n  id: 'vh-005',\n  make: 'Tesla',\n  model: 'Model 3',\n  year: 2023,\n  price: 55000,\n  status: 'verfügbar',\n  type: 'Limousine',\n  fuel: 'Elektro',\n  km: 5000,\n  color: 'Rot',\n  vin: 'TES123456789',\n  location: 'Stellplatz C1',\n  smartcarConnected: true,\n  battery: 85\n}];\n\n// Generiere weitere Mock-Fahrzeuge bis 80\nfunction generateMockVehicles() {\n  const vehicles = [...mockVehicles];\n  const makes = ['BMW', 'Mercedes', 'Audi', 'Volkswagen', 'Opel', 'Ford', 'Renault', 'Peugeot', 'Skoda', 'Seat'];\n  const models = ['Limousine', 'Kombi', 'SUV', 'Kompakt', 'Cabrio'];\n  const colors = ['Schwarz', 'Weiß', 'Silber', 'Blau', 'Rot', 'Grau', 'Grün'];\n  const fuels = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];\n  const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];\n  for (let i = 6; i <= 80; i++) {\n    const make = makes[Math.floor(Math.random() * makes.length)];\n    const model = models[Math.floor(Math.random() * models.length)];\n    const year = 2020 + Math.floor(Math.random() * 4);\n    const price = 20000 + Math.floor(Math.random() * 60000);\n    const km = Math.floor(Math.random() * 100000);\n    const color = colors[Math.floor(Math.random() * colors.length)];\n    const fuel = fuels[Math.floor(Math.random() * fuels.length)];\n    const status = statuses[Math.floor(Math.random() * statuses.length)];\n    vehicles.push({\n      id: `vh-${i.toString().padStart(3, '0')}`,\n      make,\n      model: `${make} ${model}`,\n      year,\n      price,\n      status,\n      type: model,\n      fuel,\n      km,\n      color,\n      vin: `VIN${i.toString().padStart(9, '0')}`,\n      location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i / 10))}${i % 10 + 1}`,\n      smartcarConnected: Math.random() < 0.1 // 10% haben Smartcar\n    });\n  }\n  return vehicles;\n}\nclass VehicleService {\n  constructor() {\n    this.vehicles = this.loadVehicles();\n  }\n  loadVehicles() {\n    // Lade aus localStorage oder verwende Mock-Daten\n    const stored = localStorage.getItem('autohaus_vehicles');\n    if (stored) {\n      return JSON.parse(stored);\n    }\n    const vehicles = generateMockVehicles();\n    this.saveVehicles(vehicles);\n    return vehicles;\n  }\n  saveVehicles(vehicles = this.vehicles) {\n    localStorage.setItem('autohaus_vehicles', JSON.stringify(vehicles));\n  }\n  getAllVehicles() {\n    return this.vehicles;\n  }\n  getVehicleById(id) {\n    return this.vehicles.find(v => v.id === id);\n  }\n  getVehiclesByStatus(status) {\n    return this.vehicles.filter(v => v.status === status);\n  }\n  searchVehicles(query) {\n    const q = query.toLowerCase();\n    return this.vehicles.filter(v => v.make.toLowerCase().includes(q) || v.model.toLowerCase().includes(q) || v.color.toLowerCase().includes(q) || v.vin.toLowerCase().includes(q));\n  }\n  addVehicle(vehicle) {\n    const newId = `vh-${(this.vehicles.length + 1).toString().padStart(3, '0')}`;\n    const newVehicle = {\n      ...vehicle,\n      id: newId\n    };\n    this.vehicles.push(newVehicle);\n    this.saveVehicles();\n    return newVehicle;\n  }\n  updateVehicle(id, updates) {\n    const index = this.vehicles.findIndex(v => v.id === id);\n    if (index !== -1) {\n      this.vehicles[index] = {\n        ...this.vehicles[index],\n        ...updates\n      };\n      this.saveVehicles();\n      return this.vehicles[index];\n    }\n    return null;\n  }\n  deleteVehicle(id) {\n    const index = this.vehicles.findIndex(v => v.id === id);\n    if (index !== -1) {\n      const deleted = this.vehicles.splice(index, 1)[0];\n      this.saveVehicles();\n      return deleted;\n    }\n    return null;\n  }\n  getStatistics() {\n    const total = this.vehicles.length;\n    const available = this.vehicles.filter(v => v.status === 'verfügbar').length;\n    const sold = this.vehicles.filter(v => v.status === 'verkauft').length;\n    const reserved = this.vehicles.filter(v => v.status === 'reserviert').length;\n    const workshop = this.vehicles.filter(v => v.status === 'werkstatt').length;\n    const smartcarConnected = this.vehicles.filter(v => v.smartcarConnected).length;\n    return {\n      total,\n      available,\n      sold,\n      reserved,\n      workshop,\n      smartcarConnected\n    };\n  }\n}\nexport default new VehicleService();", "map": {"version": 3, "names": ["mockVehicles", "id", "make", "model", "year", "price", "status", "type", "fuel", "km", "color", "vin", "location", "smartcarConnected", "battery", "generateMockVehicles", "vehicles", "makes", "models", "colors", "fuels", "statuses", "i", "Math", "floor", "random", "length", "push", "toString", "padStart", "String", "fromCharCode", "VehicleService", "constructor", "loadVehicles", "stored", "localStorage", "getItem", "JSON", "parse", "saveVehicles", "setItem", "stringify", "getAllVehicles", "getVehicleById", "find", "v", "getVehiclesByStatus", "filter", "searchVehicles", "query", "q", "toLowerCase", "includes", "addVehicle", "vehicle", "newId", "newVehicle", "updateVehicle", "updates", "index", "findIndex", "deleteVehicle", "deleted", "splice", "getStatistics", "total", "available", "sold", "reserved", "workshop"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/services/vehicleService.js"], "sourcesContent": ["// Fahrzeug-Service für lokale Datenverwaltung\n\n// Mock-Daten für Autohaus-Fahrzeuge\nconst mockVehicles = [\n  // Beispiel-Fahrzeuge für dein Autohaus\n  { id: 'vh-001', make: 'BMW', model: '320i', year: 2023, price: 45000, status: 'verfügbar', type: 'Limousine', fuel: 'Benzin', km: 12500, color: 'Schwarz', vin: 'WBA123456789', location: 'Stellplatz A1' },\n  { id: 'vh-002', make: 'Mercedes', model: 'C-Klasse', year: 2022, price: 52000, status: 'verkauft', type: 'Limousine', fuel: 'Diesel', km: 25000, color: 'Silber', vin: 'WDD987654321', location: 'Stellplatz A2' },\n  { id: 'vh-003', make: 'Audi', model: 'A4', year: 2023, price: 48000, status: 'reserviert', type: 'Limousine', fuel: 'Benzin', km: 8500, color: 'Weiß', vin: 'WAU456789123', location: 'Stellplatz B1' },\n  { id: 'vh-004', make: 'Volkswagen', model: 'Golf', year: 2022, price: 28000, status: 'verfügbar', type: 'Kompakt', fuel: 'Benzin', km: 18000, color: 'Blau', vin: 'WVW789123456', location: 'Stellplatz B2' },\n  { id: 'vh-005', make: 'Tesla', model: 'Model 3', year: 2023, price: 55000, status: 'verfügbar', type: 'Limousine', fuel: 'Elektro', km: 5000, color: 'Rot', vin: 'TES123456789', location: 'Stellplatz C1', smartcarConnected: true, battery: 85 }\n];\n\n// Generiere weitere Mock-Fahrzeuge bis 80\nfunction generateMockVehicles() {\n  const vehicles = [...mockVehicles];\n  const makes = ['BMW', 'Mercedes', 'Audi', 'Volkswagen', 'Opel', 'Ford', 'Renault', 'Peugeot', 'Skoda', 'Seat'];\n  const models = ['Limousine', 'Kombi', 'SUV', 'Kompakt', 'Cabrio'];\n  const colors = ['Schwarz', 'Weiß', 'Silber', 'Blau', 'Rot', 'Grau', 'Grün'];\n  const fuels = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];\n  const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];\n  \n  for (let i = 6; i <= 80; i++) {\n    const make = makes[Math.floor(Math.random() * makes.length)];\n    const model = models[Math.floor(Math.random() * models.length)];\n    const year = 2020 + Math.floor(Math.random() * 4);\n    const price = 20000 + Math.floor(Math.random() * 60000);\n    const km = Math.floor(Math.random() * 100000);\n    const color = colors[Math.floor(Math.random() * colors.length)];\n    const fuel = fuels[Math.floor(Math.random() * fuels.length)];\n    const status = statuses[Math.floor(Math.random() * statuses.length)];\n    \n    vehicles.push({\n      id: `vh-${i.toString().padStart(3, '0')}`,\n      make,\n      model: `${make} ${model}`,\n      year,\n      price,\n      status,\n      type: model,\n      fuel,\n      km,\n      color,\n      vin: `VIN${i.toString().padStart(9, '0')}`,\n      location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i/10))}${(i%10)+1}`,\n      smartcarConnected: Math.random() < 0.1 // 10% haben Smartcar\n    });\n  }\n  \n  return vehicles;\n}\n\nclass VehicleService {\n  constructor() {\n    this.vehicles = this.loadVehicles();\n  }\n  \n  loadVehicles() {\n    // Lade aus localStorage oder verwende Mock-Daten\n    const stored = localStorage.getItem('autohaus_vehicles');\n    if (stored) {\n      return JSON.parse(stored);\n    }\n    const vehicles = generateMockVehicles();\n    this.saveVehicles(vehicles);\n    return vehicles;\n  }\n  \n  saveVehicles(vehicles = this.vehicles) {\n    localStorage.setItem('autohaus_vehicles', JSON.stringify(vehicles));\n  }\n  \n  getAllVehicles() {\n    return this.vehicles;\n  }\n  \n  getVehicleById(id) {\n    return this.vehicles.find(v => v.id === id);\n  }\n  \n  getVehiclesByStatus(status) {\n    return this.vehicles.filter(v => v.status === status);\n  }\n  \n  searchVehicles(query) {\n    const q = query.toLowerCase();\n    return this.vehicles.filter(v => \n      v.make.toLowerCase().includes(q) ||\n      v.model.toLowerCase().includes(q) ||\n      v.color.toLowerCase().includes(q) ||\n      v.vin.toLowerCase().includes(q)\n    );\n  }\n  \n  addVehicle(vehicle) {\n    const newId = `vh-${(this.vehicles.length + 1).toString().padStart(3, '0')}`;\n    const newVehicle = { ...vehicle, id: newId };\n    this.vehicles.push(newVehicle);\n    this.saveVehicles();\n    return newVehicle;\n  }\n  \n  updateVehicle(id, updates) {\n    const index = this.vehicles.findIndex(v => v.id === id);\n    if (index !== -1) {\n      this.vehicles[index] = { ...this.vehicles[index], ...updates };\n      this.saveVehicles();\n      return this.vehicles[index];\n    }\n    return null;\n  }\n  \n  deleteVehicle(id) {\n    const index = this.vehicles.findIndex(v => v.id === id);\n    if (index !== -1) {\n      const deleted = this.vehicles.splice(index, 1)[0];\n      this.saveVehicles();\n      return deleted;\n    }\n    return null;\n  }\n  \n  getStatistics() {\n    const total = this.vehicles.length;\n    const available = this.vehicles.filter(v => v.status === 'verfügbar').length;\n    const sold = this.vehicles.filter(v => v.status === 'verkauft').length;\n    const reserved = this.vehicles.filter(v => v.status === 'reserviert').length;\n    const workshop = this.vehicles.filter(v => v.status === 'werkstatt').length;\n    const smartcarConnected = this.vehicles.filter(v => v.smartcarConnected).length;\n    \n    return {\n      total,\n      available,\n      sold,\n      reserved,\n      workshop,\n      smartcarConnected\n    };\n  }\n}\n\nexport default new VehicleService();"], "mappings": "AAAA;;AAEA;AACA,MAAMA,YAAY,GAAG;AACnB;AACA;EAAEC,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,MAAM;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,WAAW;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,QAAQ;EAAEC,EAAE,EAAE,KAAK;EAAEC,KAAK,EAAE,SAAS;EAAEC,GAAG,EAAE,cAAc;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EAC3M;EAAEX,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE,UAAU;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,UAAU;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,QAAQ;EAAEC,EAAE,EAAE,KAAK;EAAEC,KAAK,EAAE,QAAQ;EAAEC,GAAG,EAAE,cAAc;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EAClN;EAAEX,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,MAAM;EAAEC,KAAK,EAAE,IAAI;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,YAAY;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,QAAQ;EAAEC,EAAE,EAAE,IAAI;EAAEC,KAAK,EAAE,MAAM;EAAEC,GAAG,EAAE,cAAc;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EACvM;EAAEX,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE,MAAM;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,WAAW;EAAEC,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,QAAQ;EAAEC,EAAE,EAAE,KAAK;EAAEC,KAAK,EAAE,MAAM;EAAEC,GAAG,EAAE,cAAc;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EAC7M;EAAEX,EAAE,EAAE,QAAQ;EAAEC,IAAI,EAAE,OAAO;EAAEC,KAAK,EAAE,SAAS;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,MAAM,EAAE,WAAW;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,SAAS;EAAEC,EAAE,EAAE,IAAI;EAAEC,KAAK,EAAE,KAAK;EAAEC,GAAG,EAAE,cAAc;EAAEC,QAAQ,EAAE,eAAe;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAG,CAAC,CACnP;;AAED;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,MAAMC,QAAQ,GAAG,CAAC,GAAGhB,YAAY,CAAC;EAClC,MAAMiB,KAAK,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;EAC9G,MAAMC,MAAM,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC;EACjE,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;EAC3E,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;EACvD,MAAMC,QAAQ,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC;EAErE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC5B,MAAMpB,IAAI,GAAGe,KAAK,CAACM,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGR,KAAK,CAACS,MAAM,CAAC,CAAC;IAC5D,MAAMvB,KAAK,GAAGe,MAAM,CAACK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGP,MAAM,CAACQ,MAAM,CAAC,CAAC;IAC/D,MAAMtB,IAAI,GAAG,IAAI,GAAGmB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACjD,MAAMpB,KAAK,GAAG,KAAK,GAAGkB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC;IACvD,MAAMhB,EAAE,GAAGc,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;IAC7C,MAAMf,KAAK,GAAGS,MAAM,CAACI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC;IAC/D,MAAMlB,IAAI,GAAGY,KAAK,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC;IAC5D,MAAMpB,MAAM,GAAGe,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,QAAQ,CAACK,MAAM,CAAC,CAAC;IAEpEV,QAAQ,CAACW,IAAI,CAAC;MACZ1B,EAAE,EAAE,MAAMqB,CAAC,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MACzC3B,IAAI;MACJC,KAAK,EAAE,GAAGD,IAAI,IAAIC,KAAK,EAAE;MACzBC,IAAI;MACJC,KAAK;MACLC,MAAM;MACNC,IAAI,EAAEJ,KAAK;MACXK,IAAI;MACJC,EAAE;MACFC,KAAK;MACLC,GAAG,EAAE,MAAMW,CAAC,CAACM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAC1CjB,QAAQ,EAAE,cAAckB,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGR,IAAI,CAACC,KAAK,CAACF,CAAC,GAAC,EAAE,CAAC,CAAC,GAAIA,CAAC,GAAC,EAAE,GAAE,CAAC,EAAE;MAC/ET,iBAAiB,EAAEU,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA,OAAOT,QAAQ;AACjB;AAEA,MAAMgB,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAACkB,YAAY,CAAC,CAAC;EACrC;EAEAA,YAAYA,CAAA,EAAG;IACb;IACA,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACxD,IAAIF,MAAM,EAAE;MACV,OAAOG,IAAI,CAACC,KAAK,CAACJ,MAAM,CAAC;IAC3B;IACA,MAAMnB,QAAQ,GAAGD,oBAAoB,CAAC,CAAC;IACvC,IAAI,CAACyB,YAAY,CAACxB,QAAQ,CAAC;IAC3B,OAAOA,QAAQ;EACjB;EAEAwB,YAAYA,CAACxB,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IACrCoB,YAAY,CAACK,OAAO,CAAC,mBAAmB,EAAEH,IAAI,CAACI,SAAS,CAAC1B,QAAQ,CAAC,CAAC;EACrE;EAEA2B,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3B,QAAQ;EACtB;EAEA4B,cAAcA,CAAC3C,EAAE,EAAE;IACjB,OAAO,IAAI,CAACe,QAAQ,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,EAAE,KAAKA,EAAE,CAAC;EAC7C;EAEA8C,mBAAmBA,CAACzC,MAAM,EAAE;IAC1B,OAAO,IAAI,CAACU,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAKA,MAAM,CAAC;EACvD;EAEA2C,cAAcA,CAACC,KAAK,EAAE;IACpB,MAAMC,CAAC,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;IAC7B,OAAO,IAAI,CAACpC,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAC3BA,CAAC,CAAC5C,IAAI,CAACkD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IAChCL,CAAC,CAAC3C,KAAK,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IACjCL,CAAC,CAACpC,KAAK,CAAC0C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IACjCL,CAAC,CAACnC,GAAG,CAACyC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAChC,CAAC;EACH;EAEAG,UAAUA,CAACC,OAAO,EAAE;IAClB,MAAMC,KAAK,GAAG,MAAM,CAAC,IAAI,CAACxC,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAEE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5E,MAAM4B,UAAU,GAAG;MAAE,GAAGF,OAAO;MAAEtD,EAAE,EAAEuD;IAAM,CAAC;IAC5C,IAAI,CAACxC,QAAQ,CAACW,IAAI,CAAC8B,UAAU,CAAC;IAC9B,IAAI,CAACjB,YAAY,CAAC,CAAC;IACnB,OAAOiB,UAAU;EACnB;EAEAC,aAAaA,CAACzD,EAAE,EAAE0D,OAAO,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAI,CAAC5C,QAAQ,CAAC6C,SAAS,CAACf,CAAC,IAAIA,CAAC,CAAC7C,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAI2D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC5C,QAAQ,CAAC4C,KAAK,CAAC,GAAG;QAAE,GAAG,IAAI,CAAC5C,QAAQ,CAAC4C,KAAK,CAAC;QAAE,GAAGD;MAAQ,CAAC;MAC9D,IAAI,CAACnB,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI,CAACxB,QAAQ,CAAC4C,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb;EAEAE,aAAaA,CAAC7D,EAAE,EAAE;IAChB,MAAM2D,KAAK,GAAG,IAAI,CAAC5C,QAAQ,CAAC6C,SAAS,CAACf,CAAC,IAAIA,CAAC,CAAC7C,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAI2D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAMG,OAAO,GAAG,IAAI,CAAC/C,QAAQ,CAACgD,MAAM,CAACJ,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,IAAI,CAACpB,YAAY,CAAC,CAAC;MACnB,OAAOuB,OAAO;IAChB;IACA,OAAO,IAAI;EACb;EAEAE,aAAaA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG,IAAI,CAAClD,QAAQ,CAACU,MAAM;IAClC,MAAMyC,SAAS,GAAG,IAAI,CAACnD,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,CAACoB,MAAM;IAC5E,MAAM0C,IAAI,GAAG,IAAI,CAACpD,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,UAAU,CAAC,CAACoB,MAAM;IACtE,MAAM2C,QAAQ,GAAG,IAAI,CAACrD,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,YAAY,CAAC,CAACoB,MAAM;IAC5E,MAAM4C,QAAQ,GAAG,IAAI,CAACtD,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,CAACoB,MAAM;IAC3E,MAAMb,iBAAiB,GAAG,IAAI,CAACG,QAAQ,CAACgC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACjC,iBAAiB,CAAC,CAACa,MAAM;IAE/E,OAAO;MACLwC,KAAK;MACLC,SAAS;MACTC,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRzD;IACF,CAAC;EACH;AACF;AAEA,eAAe,IAAImB,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}