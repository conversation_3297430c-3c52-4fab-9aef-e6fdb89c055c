import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { FaArrowLeft, FaBatteryHalf, FaMapMarkerAlt, FaTachometerAlt, FaWrench } from 'react-icons/fa';

function VehicleDetails() {
  const { id } = useParams();
  const [vehicle, setVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadVehicleDetails();
  }, [id]);

  const loadVehicleDetails = async () => {
    try {
      // Für Demo verwenden wir Mock-Daten
      setTimeout(() => {
        setVehicle({
          id: id,
          make: 'Tesla',
          model: 'Model 3',
          year: 2023,
          battery: 85,
          location: 'Hamburg, Deutschland',
          odometer: 15420,
          tirePressure: {
            frontLeft: 2.3,
            frontRight: 2.2,
            rearLeft: 2.4,
            rearRight: 2.3
          },
          lastUpdate: new Date().toISOString()
        });
        setLoading(false);
      }, 1000);
    } catch (err) {
      setError('<PERSON><PERSON> beim <PERSON> der Fahrzeugdaten');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
        <h2>Lade Fahrzeugdetails...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container" style={{ padding: '2rem' }}>
        <div className="card" style={{ textAlign: 'center' }}>
          <h2 style={{ color: 'var(--error-color)' }}>Fehler</h2>
          <p>{error}</p>
          <Link to="/" className="btn btn-primary">Zurück zum Dashboard</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container" style={{ padding: '2rem' }}>
      <header style={{ marginBottom: '2rem', display: 'flex', alignItems: 'center' }}>
        <Link to="/" style={{ marginRight: '1rem', color: 'var(--primary-color)' }}>
          <FaArrowLeft size={20} />
        </Link>
        <div>
          <h1>{vehicle.make} {vehicle.model}</h1>
          <p style={{ color: 'var(--text-secondary)', margin: 0 }}>
            Baujahr {vehicle.year} • Letzte Aktualisierung: {new Date(vehicle.lastUpdate).toLocaleString('de-DE')}
          </p>
        </div>
      </header>

      <div className="grid grid-2">
        {/* Batteriestand */}
        <div className="card">
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
            <FaBatteryHalf style={{ color: 'var(--success-color)', marginRight: '0.5rem' }} />
            <h3>Batteriestand</h3>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--success-color)' }}>
              {vehicle.battery}%
            </div>
            <div style={{ 
              width: '100%', 
              height: '20px', 
              backgroundColor: 'var(--border-color)', 
              borderRadius: '10px',
              overflow: 'hidden',
              marginTop: '1rem'
            }}>
              <div style={{
                width: `${vehicle.battery}%`,
                height: '100%',
                backgroundColor: vehicle.battery > 50 ? 'var(--success-color)' : 
                                vehicle.battery > 20 ? 'var(--warning-color)' : 'var(--error-color)',
                transition: 'width 0.3s ease'
              }}></div>
            </div>
          </div>
        </div>

        {/* Standort */}
        <div className="card">
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
            <FaMapMarkerAlt style={{ color: 'var(--warning-color)', marginRight: '0.5rem' }} />
            <h3>Standort</h3>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
              {vehicle.location}
            </div>
            <div style={{ 
              marginTop: '1rem', 
              padding: '1rem', 
              backgroundColor: 'var(--bg-color)', 
              borderRadius: '6px',
              color: 'var(--text-secondary)'
            }}>
              📍 Karte wird geladen...
            </div>
          </div>
        </div>

        {/* Kilometerstand */}
        <div className="card">
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
            <FaTachometerAlt style={{ color: 'var(--secondary-color)', marginRight: '0.5rem' }} />
            <h3>Kilometerstand</h3>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold' }}>
              {vehicle.odometer.toLocaleString('de-DE')} km
            </div>
            <div style={{ color: 'var(--text-secondary)', marginTop: '0.5rem' }}>
              Gesamtkilometer
            </div>
          </div>
        </div>

        {/* Reifendruck */}
        <div className="card">
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
            <FaWrench style={{ color: 'var(--secondary-color)', marginRight: '0.5rem' }} />
            <h3>Reifendruck</h3>
          </div>
          <div className="grid grid-2">
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontWeight: 'bold' }}>Vorne Links</div>
              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>
                {vehicle.tirePressure.frontLeft} bar
              </div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontWeight: 'bold' }}>Vorne Rechts</div>
              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>
                {vehicle.tirePressure.frontRight} bar
              </div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontWeight: 'bold' }}>Hinten Links</div>
              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>
                {vehicle.tirePressure.rearLeft} bar
              </div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontWeight: 'bold' }}>Hinten Rechts</div>
              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>
                {vehicle.tirePressure.rearRight} bar
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style={{ marginTop: '2rem', textAlign: 'center' }}>
        <button 
          className="btn btn-primary" 
          onClick={loadVehicleDetails}
          style={{ marginRight: '1rem' }}
        >
          Daten aktualisieren
        </button>
        <Link to="/" className="btn" style={{ 
          backgroundColor: 'var(--secondary-color)', 
          color: 'white',
          textDecoration: 'none'
        }}>
          Zurück zum Dashboard
        </Link>
      </div>
    </div>
  );
}

export default VehicleDetails;