import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaCar, FaBatteryHalf, FaMapMarkerAlt, FaTachometerAlt, FaPlus, FaSearch, FaFilter, FaChartBar } from 'react-icons/fa';
import vehicleService from '../services/vehicleService';

function Dashboard() {
  const [vehicles, setVehicles] = useState([]);
  const [filteredVehicles, setFilteredVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('alle');
  const [statistics, setStatistics] = useState({});
  const [showSmartcarOnly, setShowSmartcarOnly] = useState(false);

  useEffect(() => {
    loadVehicles();
  }, []);

  useEffect(() => {
    filterVehicles();
  }, [vehicles, searchQuery, statusFilter, showSmartcarOnly]);

  const loadVehicles = async () => {
    try {
      const allVehicles = vehicleService.getAllVehicles();
      const stats = vehicleService.getStatistics();
      setVehicles(allVehicles);
      setStatistics(stats);
      setLoading(false);
    } catch (error) {
      console.error('Fehler beim Laden der Fahrzeuge:', error);
      setLoading(false);
    }
  };

  const filterVehicles = () => {
    let filtered = vehicles;
    
    // Suchfilter
    if (searchQuery) {
      filtered = vehicleService.searchVehicles(searchQuery);
    }
    
    // Status-Filter
    if (statusFilter !== 'alle') {
      filtered = filtered.filter(v => v.status === statusFilter);
    }
    
    // Smartcar-Filter
    if (showSmartcarOnly) {
      filtered = filtered.filter(v => v.smartcarConnected);
    }
    
    setFilteredVehicles(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'verfügbar': return 'var(--success-color)';
      case 'verkauft': return 'var(--secondary-color)';
      case 'reserviert': return 'var(--warning-color)';
      case 'werkstatt': return 'var(--error-color)';
      default: return 'var(--text-secondary)';
    }
  };

  if (loading) {
    return (
      <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
        <h2>Lade Fahrzeugdaten...</h2>
      </div>
    );
  }

  return (
    <div className="container" style={{ padding: '2rem' }}>
      <header style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <div>
            <h1>🚗 Autohaus Dashboard</h1>
            <p style={{ color: 'var(--text-secondary)', margin: 0 }}>
              Verwaltung aller {statistics.total} Fahrzeuge
            </p>
          </div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <Link to="/management" className="btn" style={{ backgroundColor: 'var(--warning-color)', color: 'white' }}>
              🚗 Drag & Drop Management
            </Link>
            <Link to="/vehicle/add" className="btn btn-primary">
              <FaPlus style={{ marginRight: '0.5rem' }} />
              Fahrzeug hinzufügen
            </Link>
          </div>
        </div>
        
        {/* Statistiken */}
        <div className="grid grid-2" style={{ marginBottom: '2rem' }}>
          <div className="card">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <FaChartBar style={{ color: 'var(--primary-color)', marginRight: '0.5rem' }} />
              <h3>Fahrzeug-Statistiken</h3>
            </div>
            <div className="grid grid-3">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--success-color)' }}>{statistics.available}</div>
                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verfügbar</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--warning-color)' }}>{statistics.reserved}</div>
                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Reserviert</div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--secondary-color)' }}>{statistics.sold}</div>
                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verkauft</div>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <FaBatteryHalf style={{ color: 'var(--success-color)', marginRight: '0.5rem' }} />
              <h3>Smartcar Integration</h3>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>{statistics.smartcarConnected}</div>
              <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verbundene Fahrzeuge</div>
              <Link to="/auth" className="btn" style={{ marginTop: '1rem', fontSize: '0.875rem' }}>
                Weitere verbinden
              </Link>
            </div>
          </div>
        </div>
        
        {/* Filter und Suche */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="grid grid-2">
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>Suche</label>
              <div style={{ position: 'relative' }}>
                <FaSearch style={{ position: 'absolute', left: '1rem', top: '50%', transform: 'translateY(-50%)', color: 'var(--text-secondary)' }} />
                <input
                  type="text"
                  placeholder="Marke, Modell, Farbe oder VIN..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.75rem 1rem 0.75rem 2.5rem',
                    border: '1px solid var(--border-color)',
                    borderRadius: '6px',
                    fontSize: '1rem'
                  }}
                />
              </div>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>Filter</label>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  style={{
                    flex: 1,
                    padding: '0.75rem',
                    border: '1px solid var(--border-color)',
                    borderRadius: '6px',
                    fontSize: '1rem'
                  }}
                >
                  <option value="alle">Alle Status</option>
                  <option value="verfügbar">Verfügbar</option>
                  <option value="reserviert">Reserviert</option>
                  <option value="verkauft">Verkauft</option>
                  <option value="werkstatt">Werkstatt</option>
                </select>
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', whiteSpace: 'nowrap' }}>
                  <input
                    type="checkbox"
                    checked={showSmartcarOnly}
                    onChange={(e) => setShowSmartcarOnly(e.target.checked)}
                  />
                  Nur Smartcar
                </label>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="grid grid-3">
        {filteredVehicles.map(vehicle => (
          <div key={vehicle.id} className="card">
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
              <FaCar size={20} style={{ color: 'var(--primary-color)', marginRight: '0.5rem' }} />
              <div style={{ flex: 1 }}>
                <h3 style={{ margin: 0, fontSize: '1.1rem' }}>{vehicle.make}</h3>
                <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>{vehicle.model}</div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '0.875rem', fontWeight: 'bold', color: getStatusColor(vehicle.status) }}>
                  {vehicle.status.toUpperCase()}
                </div>
                <div style={{ fontSize: '0.75rem', color: 'var(--text-secondary)' }}>{vehicle.year}</div>
              </div>
            </div>
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Preis:</span>
                <span style={{ fontWeight: 'bold' }}>{vehicle.price?.toLocaleString('de-DE')} €</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>KM:</span>
                <span>{vehicle.km?.toLocaleString('de-DE')} km</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Kraftstoff:</span>
                <span>{vehicle.fuel}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Standort:</span>
                <span style={{ fontSize: '0.875rem' }}>{vehicle.location}</span>
              </div>
              {vehicle.smartcarConnected && (
                <div style={{ display: 'flex', alignItems: 'center', marginTop: '0.5rem', padding: '0.5rem', backgroundColor: 'var(--success-color)', color: 'white', borderRadius: '4px', fontSize: '0.875rem' }}>
                  <FaBatteryHalf style={{ marginRight: '0.5rem' }} />
                  Smartcar verbunden {vehicle.battery && `(${vehicle.battery}%)`}
                </div>
              )}
            </div>

            <Link 
              to={`/vehicle/${vehicle.id}`} 
              className="btn btn-primary" 
              style={{ width: '100%', textAlign: 'center', textDecoration: 'none' }}
            >
              Details anzeigen
            </Link>
          </div>
        ))}
      </div>

      {filteredVehicles.length === 0 && (
        <div className="card" style={{ textAlign: 'center', gridColumn: '1 / -1' }}>
          <h3>Keine Fahrzeuge gefunden</h3>
          <p style={{ color: 'var(--text-secondary)', margin: '1rem 0' }}>
            {searchQuery || statusFilter !== 'alle' || showSmartcarOnly 
              ? 'Keine Fahrzeuge entsprechen den aktuellen Filterkriterien.' 
              : 'Noch keine Fahrzeuge im System.'}
          </p>
          {(!searchQuery && statusFilter === 'alle' && !showSmartcarOnly) && (
            <Link to="/vehicle/add" className="btn btn-primary">
              Erstes Fahrzeug hinzufügen
            </Link>
          )}
        </div>
      )}
    </div>
  );
}

export default Dashboard;