// API Service für echte Fahrzeugdaten von automobile-nord.com und mobile.de

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Hilfsfunktion für API-Aufrufe
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Fahrzeuge von automobile-nord.com scrapen
  async scrapeAutomobileNord(importData = false) {
    try {
      console.log('Scraping vehicles from automobile-nord.com...');
      const response = await this.makeRequest('/api/scraping/automobile-nord', {
        method: 'POST',
        body: JSON.stringify({ import: importData }),
      });
      
      return response;
    } catch (error) {
      console.error('Error scraping automobile-nord.com:', error);
      throw error;
    }
  }

  // Marktdaten von mobile.de scrapen
  async scrapeMobileDe(searchQuery = '') {
    try {
      console.log('Scraping market data from mobile.de...');
      const response = await this.makeRequest('/api/scraping/mobile-de', {
        method: 'POST',
        body: JSON.stringify({ search: searchQuery }),
      });
      
      return response;
    } catch (error) {
      console.error('Error scraping mobile.de:', error);
      throw error;
    }
  }

  // Alle Fahrzeuge vom Backend abrufen
  async getAllVehicles(params = {}) {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const endpoint = `/api/vehicles${queryParams ? `?${queryParams}` : ''}`;
      
      const response = await this.makeRequest(endpoint);
      return response;
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      throw error;
    }
  }

  // Einzelnes Fahrzeug abrufen
  async getVehicleById(id) {
    try {
      const response = await this.makeRequest(`/api/vehicles/${id}`);
      return response;
    } catch (error) {
      console.error(`Error fetching vehicle ${id}:`, error);
      throw error;
    }
  }

  // Neues Fahrzeug erstellen
  async createVehicle(vehicleData) {
    try {
      const response = await this.makeRequest('/api/vehicles', {
        method: 'POST',
        body: JSON.stringify(vehicleData),
      });
      
      return response;
    } catch (error) {
      console.error('Error creating vehicle:', error);
      throw error;
    }
  }

  // Fahrzeug aktualisieren
  async updateVehicle(id, updates) {
    try {
      const response = await this.makeRequest(`/api/vehicles/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      
      return response;
    } catch (error) {
      console.error(`Error updating vehicle ${id}:`, error);
      throw error;
    }
  }

  // Fahrzeug löschen
  async deleteVehicle(id) {
    try {
      const response = await this.makeRequest(`/api/vehicles/${id}`, {
        method: 'DELETE',
      });
      
      return response;
    } catch (error) {
      console.error(`Error deleting vehicle ${id}:`, error);
      throw error;
    }
  }

  // Marktpreisanalyse für ein Fahrzeug
  async getMarketAnalysis(make, model, year) {
    try {
      const response = await this.makeRequest('/api/scraping/market-analysis', {
        method: 'POST',
        body: JSON.stringify({ make, model, year }),
      });
      
      return response;
    } catch (error) {
      console.error('Error getting market analysis:', error);
      throw error;
    }
  }

  // Bulk-Import mit Marktanalyse
  async bulkImportWithAnalysis() {
    try {
      console.log('Starting bulk import with market analysis...');
      const response = await this.makeRequest('/api/scraping/bulk-import', {
        method: 'POST',
      });
      
      return response;
    } catch (error) {
      console.error('Error during bulk import:', error);
      throw error;
    }
  }

  // Backend-Gesundheitsstatus prüfen
  async checkHealth() {
    try {
      const response = await this.makeRequest('/api/health');
      return response;
    } catch (error) {
      console.error('Backend health check failed:', error);
      return { status: 'error', message: 'Backend nicht erreichbar' };
    }
  }

  // Fahrzeugstatistiken berechnen (clientseitig)
  calculateStatistics(vehicles) {
    if (!vehicles || vehicles.length === 0) {
      return {
        total: 0,
        available: 0,
        sold: 0,
        reserved: 0,
        workshop: 0,
        smartcarConnected: 0
      };
    }

    const total = vehicles.length;
    const available = vehicles.filter(v => v.status === 'In Inventory' || v.status === 'verfügbar').length;
    const sold = vehicles.filter(v => v.status === 'Sold' || v.status === 'verkauft').length;
    const reserved = vehicles.filter(v => v.status === 'reserviert').length;
    const workshop = vehicles.filter(v => v.status === 'Maintenance' || v.status === 'werkstatt').length;
    const smartcarConnected = vehicles.filter(v => v.smartcarConnected).length;

    return {
      total,
      available,
      sold,
      reserved,
      workshop,
      smartcarConnected
    };
  }

  // Fahrzeuge durchsuchen (clientseitig)
  searchVehicles(vehicles, query) {
    if (!query || !vehicles) return vehicles;
    
    const q = query.toLowerCase();
    return vehicles.filter(v => 
      (v.make && v.make.toLowerCase().includes(q)) ||
      (v.model && v.model.toLowerCase().includes(q)) ||
      (v.color && v.color.toLowerCase().includes(q)) ||
      (v.vin && v.vin.toLowerCase().includes(q)) ||
      (v.licensePlate && v.licensePlate.toLowerCase().includes(q))
    );
  }
}

const apiService = new ApiService();
export default apiService;
