{"ast": null, "code": "'use strict';\n\nvar parent = require('../stable/global-this');\nmodule.exports = parent;", "map": {"version": 3, "names": ["parent", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/node_modules/core-js-pure/actual/global-this.js"], "sourcesContent": ["'use strict';\nvar parent = require('../stable/global-this');\n\nmodule.exports = parent;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAE7CC,MAAM,CAACC,OAAO,GAAGH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}