[{"C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js": "3", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js": "4", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js": "5", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js": "6", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js": "7"}, {"size": 260, "mtime": 1754921998276, "results": "8", "hashOfConfig": "9"}, {"size": 726, "mtime": 1754923721806, "results": "10", "hashOfConfig": "9"}, {"size": 11490, "mtime": 1754923800048, "results": "11", "hashOfConfig": "9"}, {"size": 7038, "mtime": 1754922448511, "results": "12", "hashOfConfig": "9"}, {"size": 6688, "mtime": 1754922448717, "results": "13", "hashOfConfig": "9"}, {"size": 6956, "mtime": 1754923688739, "results": "14", "hashOfConfig": "9"}, {"size": 5173, "mtime": 1754923367396, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mgzdnk", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js", ["37"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js", ["38"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js", ["39"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js", ["40", "41"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js", ["42"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "43", "line": 208, "column": 12, "nodeType": null}, {"ruleId": "44", "severity": 1, "message": "45", "line": 13, "column": 6, "nodeType": "46", "endLine": 13, "endColumn": 10, "suggestions": "47"}, {"ruleId": "44", "severity": 1, "message": "48", "line": 23, "column": 6, "nodeType": "46", "endLine": 23, "endColumn": 20, "suggestions": "49"}, {"ruleId": "50", "severity": 1, "message": "51", "line": 2, "column": 70, "nodeType": "52", "messageId": "53", "endLine": 2, "endColumn": 76}, {"ruleId": "50", "severity": 1, "message": "54", "line": 17, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 17, "endColumn": 20}, {"ruleId": "55", "severity": 1, "message": "56", "line": 141, "column": 1, "nodeType": "57", "endLine": 141, "endColumn": 37}, "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (208:12)", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadVehicleDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["58"], "React Hook useEffect has a missing dependency: 'handleCallback'. Either include it or remove the dependency array.", ["59"], "no-unused-vars", "'FaPlus' is defined but never used.", "Identifier", "unusedVar", "'statistics' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "60", "fix": "61"}, {"desc": "62", "fix": "63"}, "Update the dependencies array to be: [id, loadVehicleDetails]", {"range": "64", "text": "65"}, "Update the dependencies array to be: [handleCallback, searchParams]", {"range": "66", "text": "67"}, [459, 463], "[id, loadVehicleDetails]", [696, 710], "[handleCallback, searchParams]"]