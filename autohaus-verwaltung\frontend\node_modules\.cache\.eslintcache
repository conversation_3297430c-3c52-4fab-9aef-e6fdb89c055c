[{"C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js": "3", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js": "4", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js": "5", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js": "6", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js": "7"}, {"size": 260, "mtime": 1754921998276, "results": "8", "hashOfConfig": "9"}, {"size": 726, "mtime": 1754923721806, "results": "10", "hashOfConfig": "9"}, {"size": 11456, "mtime": 1754924211506, "results": "11", "hashOfConfig": "9"}, {"size": 7038, "mtime": 1754922448511, "results": "12", "hashOfConfig": "9"}, {"size": 6688, "mtime": 1754922448717, "results": "13", "hashOfConfig": "9"}, {"size": 6820, "mtime": 1754924080061, "results": "14", "hashOfConfig": "9"}, {"size": 5212, "mtime": 1754924047847, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mgzdnk", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js", ["37"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js", ["38"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js", [], [], {"ruleId": "39", "severity": 1, "message": "40", "line": 13, "column": 6, "nodeType": "41", "endLine": 13, "endColumn": 10, "suggestions": "42"}, {"ruleId": "39", "severity": 1, "message": "43", "line": 23, "column": 6, "nodeType": "41", "endLine": 23, "endColumn": 20, "suggestions": "44"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadVehicleDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["45"], "React Hook useEffect has a missing dependency: 'handleCallback'. Either include it or remove the dependency array.", ["46"], {"desc": "47", "fix": "48"}, {"desc": "49", "fix": "50"}, "Update the dependencies array to be: [id, loadVehicleDetails]", {"range": "51", "text": "52"}, "Update the dependencies array to be: [handleCallback, searchParams]", {"range": "53", "text": "54"}, [459, 463], "[id, loadVehicleDetails]", [696, 710], "[handleCallback, searchParams]"]