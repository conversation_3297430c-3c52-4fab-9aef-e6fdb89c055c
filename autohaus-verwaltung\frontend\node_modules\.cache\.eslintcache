[{"C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js": "3", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js": "4", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js": "5", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js": "6", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js": "7", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\realVehicleService.js": "8", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\apiService.js": "9", "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\mockBackendService.js": "10"}, {"size": 260, "mtime": 1754921998276, "results": "11", "hashOfConfig": "12"}, {"size": 726, "mtime": 1754923721806, "results": "13", "hashOfConfig": "12"}, {"size": 13984, "mtime": 1754925114398, "results": "14", "hashOfConfig": "12"}, {"size": 7038, "mtime": 1754922448511, "results": "15", "hashOfConfig": "12"}, {"size": 6688, "mtime": 1754922448717, "results": "16", "hashOfConfig": "12"}, {"size": 6820, "mtime": 1754924080061, "results": "17", "hashOfConfig": "12"}, {"size": 5212, "mtime": 1754924047847, "results": "18", "hashOfConfig": "12"}, {"size": 9971, "mtime": 1754925099087, "results": "19", "hashOfConfig": "12"}, {"size": 6145, "mtime": 1754924731938, "results": "20", "hashOfConfig": "12"}, {"size": 9003, "mtime": 1754925060715, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mgzdnk", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Dashboard.js", ["52"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\VehicleDetails.js", ["53"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\pages\\Auth.js", ["54"], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\components\\DragDropDashboard.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\vehicleService.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\realVehicleService.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\apiService.js", [], [], "C:\\Users\\<USER>\\Downloads\\vceeeeeettttttt\\autohaus-verwaltung\\frontend\\src\\services\\mockBackendService.js", [], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 19, "column": 6, "nodeType": "57", "endLine": 19, "endColumn": 8, "suggestions": "58"}, {"ruleId": "55", "severity": 1, "message": "59", "line": 13, "column": 6, "nodeType": "57", "endLine": 13, "endColumn": 10, "suggestions": "60"}, {"ruleId": "55", "severity": 1, "message": "61", "line": 23, "column": 6, "nodeType": "57", "endLine": 23, "endColumn": 20, "suggestions": "62"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadVehicles'. Either include it or remove the dependency array.", "ArrayExpression", ["63"], "React Hook useEffect has a missing dependency: 'loadVehicleDetails'. Either include it or remove the dependency array.", ["64"], "React Hook useEffect has a missing dependency: 'handleCallback'. Either include it or remove the dependency array.", ["65"], {"desc": "66", "fix": "67"}, {"desc": "68", "fix": "69"}, {"desc": "70", "fix": "71"}, "Update the dependencies array to be: [loadVehicles]", {"range": "72", "text": "73"}, "Update the dependencies array to be: [id, loadVehicleDetails]", {"range": "74", "text": "75"}, "Update the dependencies array to be: [handleCallback, searchParams]", {"range": "76", "text": "77"}, [853, 855], "[loadVehicles]", [459, 463], "[id, loadVehicleDetails]", [696, 710], "[handleCallback, searchParams]"]