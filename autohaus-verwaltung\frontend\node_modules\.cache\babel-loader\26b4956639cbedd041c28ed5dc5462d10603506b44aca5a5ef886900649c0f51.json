{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\vceeeeeettttttt\\\\autohaus-verwaltung\\\\frontend\\\\src\\\\components\\\\DragDropDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaCar, FaWrench, FaSprayCan, FaShippingFast, FaCheckCircle } from 'react-icons/fa';\nimport vehicleService from '../services/vehicleService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STATUS_ZONES = {\n  verfügbar: {\n    name: 'Verfügbar',\n    icon: FaCheckCircle,\n    color: 'bg-green-100 border-green-300',\n    textColor: 'text-green-800'\n  },\n  werkstatt: {\n    name: 'Werkstatt',\n    icon: FaWrench,\n    color: 'bg-red-100 border-red-300',\n    textColor: 'text-red-800'\n  },\n  wäsche: {\n    name: 'Wäsche',\n    icon: FaSprayCan,\n    color: 'bg-blue-100 border-blue-300',\n    textColor: 'text-blue-800'\n  },\n  export: {\n    name: 'Export',\n    icon: FaShippingFast,\n    color: 'bg-purple-100 border-purple-300',\n    textColor: 'text-purple-800'\n  },\n  verkauft: {\n    name: 'Verkauft',\n    icon: FaCheckCircle,\n    color: 'bg-gray-100 border-gray-300',\n    textColor: 'text-gray-800'\n  },\n  reserviert: {\n    name: 'Reserviert',\n    icon: FaCar,\n    color: 'bg-yellow-100 border-yellow-300',\n    textColor: 'text-yellow-800'\n  }\n};\nfunction DragDropDashboard() {\n  _s();\n  const [vehicles, setVehicles] = useState([]);\n  const [draggedVehicle, setDraggedVehicle] = useState(null);\n  useEffect(() => {\n    loadVehicles();\n  }, []);\n  const loadVehicles = () => {\n    const allVehicles = vehicleService.getAllVehicles();\n    setVehicles(allVehicles);\n  };\n  const handleDragStart = (e, vehicle) => {\n    setDraggedVehicle(vehicle);\n    e.dataTransfer.effectAllowed = 'move';\n  };\n  const handleDragOver = e => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'move';\n  };\n  const handleDrop = (e, newStatus) => {\n    e.preventDefault();\n    if (draggedVehicle && draggedVehicle.status !== newStatus) {\n      // Update vehicle status\n      vehicleService.updateVehicle(draggedVehicle.id, {\n        status: newStatus\n      });\n\n      // Reload vehicles to reflect changes\n      loadVehicles();\n      console.log(`Fahrzeug ${draggedVehicle.make} ${draggedVehicle.model} von ${draggedVehicle.status} zu ${newStatus} verschoben`);\n    }\n    setDraggedVehicle(null);\n  };\n  const getVehiclesByStatus = status => {\n    return vehicles.filter(v => v.status === status);\n  };\n  const VehicleCard = ({\n    vehicle\n  }) => {\n    var _vehicle$price, _vehicle$km;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      draggable: true,\n      onDragStart: e => handleDragStart(e, vehicle),\n      className: \"bg-white p-3 rounded-lg shadow-sm border border-gray-200 cursor-move hover:shadow-md transition-shadow mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaCar, {\n            className: \"text-blue-500 mr-2\",\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-sm\",\n              children: vehicle.make\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: [vehicle.model, \" (\", vehicle.year, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs font-bold\",\n            children: [(_vehicle$price = vehicle.price) === null || _vehicle$price === void 0 ? void 0 : _vehicle$price.toLocaleString('de-DE'), \" \\u20AC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: [(_vehicle$km = vehicle.km) === null || _vehicle$km === void 0 ? void 0 : _vehicle$km.toLocaleString('de-DE'), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 7\n      }, this), vehicle.smartcarConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n        children: \"\\uD83D\\uDD0B Smartcar verbunden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 5\n    }, this);\n  };\n  const StatusZone = ({\n    status,\n    statusInfo\n  }) => {\n    const vehiclesInZone = getVehiclesByStatus(status);\n    const Icon = statusInfo.icon;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${statusInfo.color} border-2 border-dashed rounded-lg p-4 min-h-[300px] transition-all hover:border-solid`,\n      onDragOver: handleDragOver,\n      onDrop: e => handleDrop(e, status),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          className: `${statusInfo.textColor} mr-2`,\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${statusInfo.textColor}`,\n          children: [statusInfo.name, \" (\", vehiclesInZone.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2 max-h-64 overflow-y-auto\",\n        children: [vehiclesInZone.map(vehicle => /*#__PURE__*/_jsxDEV(VehicleCard, {\n          vehicle: vehicle\n        }, vehicle.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)), vehiclesInZone.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-center py-8 ${statusInfo.textColor} opacity-50`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            size: 32,\n            className: \"mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            children: \"Keine Fahrzeuge\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs\",\n            children: \"Fahrzeuge hierher ziehen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-2\",\n        children: \"\\uD83D\\uDE97 Fahrzeug-Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Ziehen Sie Fahrzeuge zwischen den Bereichen, um deren Status zu \\xE4ndern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 md:grid-cols-6 gap-4 mb-6\",\n      children: Object.entries(STATUS_ZONES).map(([status, info]) => {\n        const count = getVehiclesByStatus(status).length;\n        const Icon = info.icon;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-4 rounded-lg shadow-sm border\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: info.textColor,\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-500\",\n                children: info.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)\n        }, status, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: Object.entries(STATUS_ZONES).map(([status, statusInfo]) => /*#__PURE__*/_jsxDEV(StatusZone, {\n        status: status,\n        statusInfo: statusInfo\n      }, status, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5 text-blue-400\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-blue-800\",\n            children: \"Drag & Drop Funktionalit\\xE4t\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-blue-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 Fahrzeuge per Drag & Drop zwischen Bereichen verschieben\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\xC4nderungen werden automatisch gespeichert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 Smartcar-verbundene Fahrzeuge sind markiert\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n}\n_s(DragDropDashboard, \"x5HlNB9SA7N0A/MBCyrKIhbZgG4=\");\n_c = DragDropDashboard;\nexport default DragDropDashboard;\nvar _c;\n$RefreshReg$(_c, \"DragDropDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaCar", "FaWrench", "FaSprayCan", "FaShippingFast", "FaCheckCircle", "vehicleService", "jsxDEV", "_jsxDEV", "STATUS_ZONES", "verfügbar", "name", "icon", "color", "textColor", "werkstatt", "<PERSON><PERSON><PERSON>", "export", "verkauft", "reserviert", "DragDropDashboard", "_s", "vehicles", "setVehicles", "draggedVehicle", "setDraggedVehicle", "loadVehicles", "allVehicles", "getAllVehicles", "handleDragStart", "e", "vehicle", "dataTransfer", "effectAllowed", "handleDragOver", "preventDefault", "dropEffect", "handleDrop", "newStatus", "status", "updateVehicle", "id", "console", "log", "make", "model", "getVehiclesByStatus", "filter", "v", "VehicleCard", "_vehicle$price", "_vehicle$km", "draggable", "onDragStart", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "year", "price", "toLocaleString", "km", "smartcarConnected", "StatusZone", "statusInfo", "vehiclesInZone", "Icon", "onDragOver", "onDrop", "length", "map", "Object", "entries", "info", "count", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/components/DragDropDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FaCar, FaWrench, FaSprayCan, FaShippingFast, FaCheckCircle } from 'react-icons/fa';\nimport vehicleService from '../services/vehicleService';\n\nconst STATUS_ZONES = {\n  verfügbar: { name: 'Verfügbar', icon: FaCheckCircle, color: 'bg-green-100 border-green-300', textColor: 'text-green-800' },\n  werkstatt: { name: 'Werkstatt', icon: FaWrench, color: 'bg-red-100 border-red-300', textColor: 'text-red-800' },\n  wäsche: { name: 'Wäsche', icon: FaSprayCan, color: 'bg-blue-100 border-blue-300', textColor: 'text-blue-800' },\n  export: { name: 'Export', icon: FaShippingFast, color: 'bg-purple-100 border-purple-300', textColor: 'text-purple-800' },\n  verkauft: { name: 'Verkauft', icon: FaCheckCircle, color: 'bg-gray-100 border-gray-300', textColor: 'text-gray-800' },\n  reserviert: { name: 'Reserviert', icon: FaCar, color: 'bg-yellow-100 border-yellow-300', textColor: 'text-yellow-800' }\n};\n\nfunction DragDropDashboard() {\n  const [vehicles, setVehicles] = useState([]);\n  const [draggedVehicle, setDraggedVehicle] = useState(null);\n\n  useEffect(() => {\n    loadVehicles();\n  }, []);\n\n  const loadVehicles = () => {\n    const allVehicles = vehicleService.getAllVehicles();\n    setVehicles(allVehicles);\n  };\n\n  const handleDragStart = (e, vehicle) => {\n    setDraggedVehicle(vehicle);\n    e.dataTransfer.effectAllowed = 'move';\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'move';\n  };\n\n  const handleDrop = (e, newStatus) => {\n    e.preventDefault();\n    \n    if (draggedVehicle && draggedVehicle.status !== newStatus) {\n      // Update vehicle status\n      vehicleService.updateVehicle(draggedVehicle.id, { status: newStatus });\n      \n      // Reload vehicles to reflect changes\n      loadVehicles();\n      \n      console.log(`Fahrzeug ${draggedVehicle.make} ${draggedVehicle.model} von ${draggedVehicle.status} zu ${newStatus} verschoben`);\n    }\n    \n    setDraggedVehicle(null);\n  };\n\n  const getVehiclesByStatus = (status) => {\n    return vehicles.filter(v => v.status === status);\n  };\n\n  const VehicleCard = ({ vehicle }) => (\n    <div\n      draggable\n      onDragStart={(e) => handleDragStart(e, vehicle)}\n      className=\"bg-white p-3 rounded-lg shadow-sm border border-gray-200 cursor-move hover:shadow-md transition-shadow mb-2\"\n    >\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <FaCar className=\"text-blue-500 mr-2\" size={16} />\n          <div>\n            <div className=\"font-medium text-sm\">{vehicle.make}</div>\n            <div className=\"text-xs text-gray-500\">{vehicle.model} ({vehicle.year})</div>\n          </div>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-xs font-bold\">{vehicle.price?.toLocaleString('de-DE')} €</div>\n          <div className=\"text-xs text-gray-500\">{vehicle.km?.toLocaleString('de-DE')} km</div>\n        </div>\n      </div>\n      {vehicle.smartcarConnected && (\n        <div className=\"mt-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\">\n          🔋 Smartcar verbunden\n        </div>\n      )}\n    </div>\n  );\n\n  const StatusZone = ({ status, statusInfo }) => {\n    const vehiclesInZone = getVehiclesByStatus(status);\n    const Icon = statusInfo.icon;\n\n    return (\n      <div\n        className={`${statusInfo.color} border-2 border-dashed rounded-lg p-4 min-h-[300px] transition-all hover:border-solid`}\n        onDragOver={handleDragOver}\n        onDrop={(e) => handleDrop(e, status)}\n      >\n        <div className=\"flex items-center mb-4\">\n          <Icon className={`${statusInfo.textColor} mr-2`} size={20} />\n          <h3 className={`font-semibold ${statusInfo.textColor}`}>\n            {statusInfo.name} ({vehiclesInZone.length})\n          </h3>\n        </div>\n        \n        <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n          {vehiclesInZone.map(vehicle => (\n            <VehicleCard key={vehicle.id} vehicle={vehicle} />\n          ))}\n          \n          {vehiclesInZone.length === 0 && (\n            <div className={`text-center py-8 ${statusInfo.textColor} opacity-50`}>\n              <Icon size={32} className=\"mx-auto mb-2\" />\n              <p className=\"text-sm\">Keine Fahrzeuge</p>\n              <p className=\"text-xs\">Fahrzeuge hierher ziehen</p>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold mb-2\">🚗 Fahrzeug-Management</h1>\n        <p className=\"text-gray-600\">\n          Ziehen Sie Fahrzeuge zwischen den Bereichen, um deren Status zu ändern\n        </p>\n      </div>\n\n      {/* Statistiken */}\n      <div className=\"grid grid-cols-2 md:grid-cols-6 gap-4 mb-6\">\n        {Object.entries(STATUS_ZONES).map(([status, info]) => {\n          const count = getVehiclesByStatus(status).length;\n          const Icon = info.icon;\n          return (\n            <div key={status} className=\"bg-white p-4 rounded-lg shadow-sm border\">\n              <div className=\"flex items-center\">\n                <Icon className={info.textColor} size={20} />\n                <div className=\"ml-3\">\n                  <div className=\"text-2xl font-bold\">{count}</div>\n                  <div className=\"text-sm text-gray-500\">{info.name}</div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Drag & Drop Bereiche */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {Object.entries(STATUS_ZONES).map(([status, statusInfo]) => (\n          <StatusZone key={status} status={status} statusInfo={statusInfo} />\n        ))}\n      </div>\n\n      {/* Hinweis */}\n      <div className=\"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            <svg className=\"h-5 w-5 text-blue-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-blue-800\">Drag & Drop Funktionalität</h3>\n            <div className=\"mt-2 text-sm text-blue-700\">\n              <p>• Fahrzeuge per Drag & Drop zwischen Bereichen verschieben</p>\n              <p>• Änderungen werden automatisch gespeichert</p>\n              <p>• Smartcar-verbundene Fahrzeuge sind markiert</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default DragDropDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,QAAQ,gBAAgB;AAC3F,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEP,aAAa;IAAEQ,KAAK,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAiB,CAAC;EAC1HC,SAAS,EAAE;IAAEJ,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEV,QAAQ;IAAEW,KAAK,EAAE,2BAA2B;IAAEC,SAAS,EAAE;EAAe,CAAC;EAC/GE,MAAM,EAAE;IAAEL,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAET,UAAU;IAAEU,KAAK,EAAE,6BAA6B;IAAEC,SAAS,EAAE;EAAgB,CAAC;EAC9GG,MAAM,EAAE;IAAEN,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAER,cAAc;IAAES,KAAK,EAAE,iCAAiC;IAAEC,SAAS,EAAE;EAAkB,CAAC;EACxHI,QAAQ,EAAE;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEP,aAAa;IAAEQ,KAAK,EAAE,6BAA6B;IAAEC,SAAS,EAAE;EAAgB,CAAC;EACrHK,UAAU,EAAE;IAAER,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAEX,KAAK;IAAEY,KAAK,EAAE,iCAAiC;IAAEC,SAAS,EAAE;EAAkB;AACxH,CAAC;AAED,SAASM,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,WAAW,GAAGrB,cAAc,CAACsB,cAAc,CAAC,CAAC;IACnDL,WAAW,CAACI,WAAW,CAAC;EAC1B,CAAC;EAED,MAAME,eAAe,GAAGA,CAACC,CAAC,EAAEC,OAAO,KAAK;IACtCN,iBAAiB,CAACM,OAAO,CAAC;IAC1BD,CAAC,CAACE,YAAY,CAACC,aAAa,GAAG,MAAM;EACvC,CAAC;EAED,MAAMC,cAAc,GAAIJ,CAAC,IAAK;IAC5BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACE,YAAY,CAACI,UAAU,GAAG,MAAM;EACpC,CAAC;EAED,MAAMC,UAAU,GAAGA,CAACP,CAAC,EAAEQ,SAAS,KAAK;IACnCR,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAIX,cAAc,IAAIA,cAAc,CAACe,MAAM,KAAKD,SAAS,EAAE;MACzD;MACAhC,cAAc,CAACkC,aAAa,CAAChB,cAAc,CAACiB,EAAE,EAAE;QAAEF,MAAM,EAAED;MAAU,CAAC,CAAC;;MAEtE;MACAZ,YAAY,CAAC,CAAC;MAEdgB,OAAO,CAACC,GAAG,CAAC,YAAYnB,cAAc,CAACoB,IAAI,IAAIpB,cAAc,CAACqB,KAAK,QAAQrB,cAAc,CAACe,MAAM,OAAOD,SAAS,aAAa,CAAC;IAChI;IAEAb,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMqB,mBAAmB,GAAIP,MAAM,IAAK;IACtC,OAAOjB,QAAQ,CAACyB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACT,MAAM,KAAKA,MAAM,CAAC;EAClD,CAAC;EAED,MAAMU,WAAW,GAAGA,CAAC;IAAElB;EAAQ,CAAC;IAAA,IAAAmB,cAAA,EAAAC,WAAA;IAAA,oBAC9B3C,OAAA;MACE4C,SAAS;MACTC,WAAW,EAAGvB,CAAC,IAAKD,eAAe,CAACC,CAAC,EAAEC,OAAO,CAAE;MAChDuB,SAAS,EAAC,6GAA6G;MAAAC,QAAA,gBAEvH/C,OAAA;QAAK8C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD/C,OAAA;UAAK8C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC/C,OAAA,CAACP,KAAK;YAACqD,SAAS,EAAC,oBAAoB;YAACE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDpD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAK8C,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAExB,OAAO,CAACa;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDpD,OAAA;cAAK8C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAExB,OAAO,CAACc,KAAK,EAAC,IAAE,EAACd,OAAO,CAAC8B,IAAI,EAAC,GAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpD,OAAA;UAAK8C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,IAAAL,cAAA,GAAEnB,OAAO,CAAC+B,KAAK,cAAAZ,cAAA,uBAAbA,cAAA,CAAea,cAAc,CAAC,OAAO,CAAC,EAAC,SAAE;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnFpD,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,IAAAJ,WAAA,GAAEpB,OAAO,CAACiC,EAAE,cAAAb,WAAA,uBAAVA,WAAA,CAAYY,cAAc,CAAC,OAAO,CAAC,EAAC,KAAG;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL7B,OAAO,CAACkC,iBAAiB,iBACxBzD,OAAA;QAAK8C,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EAAC;MAE5E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,CACP;EAED,MAAMM,UAAU,GAAGA,CAAC;IAAE3B,MAAM;IAAE4B;EAAW,CAAC,KAAK;IAC7C,MAAMC,cAAc,GAAGtB,mBAAmB,CAACP,MAAM,CAAC;IAClD,MAAM8B,IAAI,GAAGF,UAAU,CAACvD,IAAI;IAE5B,oBACEJ,OAAA;MACE8C,SAAS,EAAE,GAAGa,UAAU,CAACtD,KAAK,wFAAyF;MACvHyD,UAAU,EAAEpC,cAAe;MAC3BqC,MAAM,EAAGzC,CAAC,IAAKO,UAAU,CAACP,CAAC,EAAES,MAAM,CAAE;MAAAgB,QAAA,gBAErC/C,OAAA;QAAK8C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC/C,OAAA,CAAC6D,IAAI;UAACf,SAAS,EAAE,GAAGa,UAAU,CAACrD,SAAS,OAAQ;UAAC0C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DpD,OAAA;UAAI8C,SAAS,EAAE,iBAAiBa,UAAU,CAACrD,SAAS,EAAG;UAAAyC,QAAA,GACpDY,UAAU,CAACxD,IAAI,EAAC,IAAE,EAACyD,cAAc,CAACI,MAAM,EAAC,GAC5C;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENpD,OAAA;QAAK8C,SAAS,EAAC,oCAAoC;QAAAC,QAAA,GAChDa,cAAc,CAACK,GAAG,CAAC1C,OAAO,iBACzBvB,OAAA,CAACyC,WAAW;UAAkBlB,OAAO,EAAEA;QAAQ,GAA7BA,OAAO,CAACU,EAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CAClD,CAAC,EAEDQ,cAAc,CAACI,MAAM,KAAK,CAAC,iBAC1BhE,OAAA;UAAK8C,SAAS,EAAE,oBAAoBa,UAAU,CAACrD,SAAS,aAAc;UAAAyC,QAAA,gBACpE/C,OAAA,CAAC6D,IAAI;YAACb,IAAI,EAAE,EAAG;YAACF,SAAS,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CpD,OAAA;YAAG8C,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1CpD,OAAA;YAAG8C,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEpD,OAAA;IAAK8C,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB/C,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/C,OAAA;QAAI8C,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAsB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEpD,OAAA;QAAG8C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpD,OAAA;MAAK8C,SAAS,EAAC,4CAA4C;MAAAC,QAAA,EACxDmB,MAAM,CAACC,OAAO,CAAClE,YAAY,CAAC,CAACgE,GAAG,CAAC,CAAC,CAAClC,MAAM,EAAEqC,IAAI,CAAC,KAAK;QACpD,MAAMC,KAAK,GAAG/B,mBAAmB,CAACP,MAAM,CAAC,CAACiC,MAAM;QAChD,MAAMH,IAAI,GAAGO,IAAI,CAAChE,IAAI;QACtB,oBACEJ,OAAA;UAAkB8C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,eACpE/C,OAAA;YAAK8C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC/C,OAAA,CAAC6D,IAAI;cAACf,SAAS,EAAEsB,IAAI,CAAC9D,SAAU;cAAC0C,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CpD,OAAA;cAAK8C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/C,OAAA;gBAAK8C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEsB;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDpD,OAAA;gBAAK8C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEqB,IAAI,CAACjE;cAAI;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAPErB,MAAM;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQX,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpD,OAAA;MAAK8C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEmB,MAAM,CAACC,OAAO,CAAClE,YAAY,CAAC,CAACgE,GAAG,CAAC,CAAC,CAAClC,MAAM,EAAE4B,UAAU,CAAC,kBACrD3D,OAAA,CAAC0D,UAAU;QAAc3B,MAAM,EAAEA,MAAO;QAAC4B,UAAU,EAAEA;MAAW,GAA/C5B,MAAM;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2C,CACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpD,OAAA;MAAK8C,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE/C,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/C,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/C,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAACwB,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAzB,QAAA,eAC/G/C,OAAA;cAAMyE,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,kIAAkI;cAACC,QAAQ,EAAC;YAAS;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpD,OAAA;UAAK8C,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/C,OAAA;YAAI8C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFpD,OAAA;YAAK8C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzC/C,OAAA;cAAA+C,QAAA,EAAG;YAA0D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEpD,OAAA;cAAA+C,QAAA,EAAG;YAA2C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClDpD,OAAA;cAAA+C,QAAA,EAAG;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvC,EAAA,CA/JQD,iBAAiB;AAAAgE,EAAA,GAAjBhE,iBAAiB;AAiK1B,eAAeA,iBAAiB;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}