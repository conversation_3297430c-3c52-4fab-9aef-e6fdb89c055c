import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FaCar, FaSpinner } from 'react-icons/fa';

function Auth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [authUrl, setAuthUrl] = useState(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    // Prüfe ob wir von Smartcar zurückkommen (mit Code)
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    
    if (code) {
      handleCallback(code, state);
    } else {
      // Lade Auth-URL
      loadAuthUrl();
    }
  }, [searchParams]);

  const loadAuthUrl = async () => {
    try {
      setLoading(true);
      // Für Demo verwenden wir eine Mock-URL
      setTimeout(() => {
        setAuthUrl('https://connect.smartcar.com/oauth/authorize?response_type=code&client_id=demo&redirect_uri=http://localhost:3000/auth&scope=read_vehicle_info');
        setLoading(false);
      }, 1000);
    } catch (err) {
      setError('Fehler beim Laden der Authentifizierungs-URL');
      setLoading(false);
    }
  };

  const handleCallback = async (code, state) => {
    try {
      setLoading(true);
      
      // Für Demo simulieren wir erfolgreiche Authentifizierung
      setTimeout(() => {
        // Token speichern (in echter App würde hier API-Call stehen)
        localStorage.setItem('smartcar_token', 'demo_token_' + Date.now());
        localStorage.setItem('smartcar_refresh_token', 'demo_refresh_' + Date.now());
        
        // Zurück zum Dashboard
        navigate('/');
      }, 2000);
    } catch (err) {
      setError('Fehler bei der Authentifizierung');
      setLoading(false);
    }
  };

  const startAuth = () => {
    if (authUrl) {
      // In echter App würde hier zur Smartcar-URL weitergeleitet
      // Für Demo simulieren wir den Callback
      const demoCode = 'demo_code_' + Date.now();
      const demoState = 'demo_state';
      
      // Simuliere Redirect zurück mit Code
      setTimeout(() => {
        handleCallback(demoCode, demoState);
      }, 1000);
    }
  };

  const logout = () => {
    localStorage.removeItem('smartcar_token');
    localStorage.removeItem('smartcar_refresh_token');
    navigate('/');
  };

  // Prüfe ob bereits authentifiziert
  const isAuthenticated = localStorage.getItem('smartcar_token');

  if (loading) {
    return (
      <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="card" style={{ maxWidth: '500px', margin: '0 auto' }}>
          <FaSpinner style={{ 
            fontSize: '3rem', 
            color: 'var(--primary-color)', 
            animation: 'spin 1s linear infinite',
            marginBottom: '1rem'
          }} />
          <h2>Verbinde mit Smartcar...</h2>
          <p style={{ color: 'var(--text-secondary)' }}>
            Bitte warten Sie, während wir die Verbindung herstellen.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="card" style={{ maxWidth: '500px', margin: '0 auto' }}>
          <h2 style={{ color: 'var(--error-color)' }}>Fehler</h2>
          <p>{error}</p>
          <button 
            className="btn btn-primary" 
            onClick={() => {
              setError(null);
              loadAuthUrl();
            }}
          >
            Erneut versuchen
          </button>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="card" style={{ maxWidth: '500px', margin: '0 auto' }}>
          <FaCar style={{ 
            fontSize: '3rem', 
            color: 'var(--success-color)', 
            marginBottom: '1rem'
          }} />
          <h2>Bereits verbunden!</h2>
          <p style={{ color: 'var(--text-secondary)', margin: '1rem 0' }}>
            Ihr Smartcar-Konto ist bereits mit der App verbunden.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
            <button 
              className="btn btn-primary" 
              onClick={() => navigate('/')}
            >
              Zum Dashboard
            </button>
            <button 
              className="btn" 
              style={{ 
                backgroundColor: 'var(--error-color)', 
                color: 'white'
              }}
              onClick={logout}
            >
              Abmelden
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container" style={{ padding: '2rem', textAlign: 'center' }}>
      <div className="card" style={{ maxWidth: '500px', margin: '0 auto' }}>
        <FaCar style={{ 
          fontSize: '4rem', 
          color: 'var(--primary-color)', 
          marginBottom: '1rem'
        }} />
        <h1>Mit Smartcar verbinden</h1>
        <p style={{ color: 'var(--text-secondary)', margin: '1.5rem 0' }}>
          Verbinden Sie Ihre Fahrzeuge mit Smartcar, um Echtzeit-Daten wie Batteriestand, 
          Standort und Kilometerstand zu überwachen.
        </p>
        
        <div style={{ 
          backgroundColor: 'var(--bg-color)', 
          padding: '1rem', 
          borderRadius: '6px',
          margin: '1.5rem 0',
          textAlign: 'left'
        }}>
          <h4>Was Sie erhalten:</h4>
          <ul style={{ margin: '0.5rem 0', paddingLeft: '1.5rem' }}>
            <li>🔋 Batteriestand in Echtzeit</li>
            <li>📍 Aktueller Fahrzeugstandort</li>
            <li>🛣️ Kilometerstand-Tracking</li>
            <li>🔧 Reifendruck-Überwachung</li>
            <li>📊 Automatische Synchronisation</li>
          </ul>
        </div>

        <button 
          className="btn btn-primary" 
          onClick={startAuth}
          disabled={!authUrl}
          style={{ width: '100%', fontSize: '1.1rem' }}
        >
          {authUrl ? 'Jetzt verbinden' : 'Lade...'}
        </button>
        
        <p style={{ 
          fontSize: '0.875rem', 
          color: 'var(--text-secondary)', 
          marginTop: '1rem'
        }}>
          Sicher und verschlüsselt über Smartcar
        </p>
      </div>
    </div>
  );
}

// CSS für Spinner-Animation
const style = document.createElement('style');
style.textContent = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);

export default Auth;