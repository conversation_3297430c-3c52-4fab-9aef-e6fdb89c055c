{"ast": null, "code": "// Echter Fahrzeug-Service für Daten von automobile-nord.com und mobile.de\nimport apiService from './apiService';\nimport mockBackendService from './mockBackendService';\nclass RealVehicleService {\n  constructor() {\n    this.vehicles = [];\n    this.isLoading = false;\n    this.lastUpdate = null;\n    this.useBackend = true;\n  }\n\n  // Prüfe Backend-Verfügbarkeit\n  async checkBackendHealth() {\n    try {\n      const health = await apiService.checkHealth();\n      this.useBackend = health.status === 'ok' || health.success;\n      return this.useBackend;\n    } catch (error) {\n      console.warn('Echtes Backend nicht erreichbar, verwende Mock-Backend');\n      // Verwende Mock-Backend als Fallback\n      try {\n        const mockHealth = await mockBackendService.checkHealth();\n        this.useBackend = false; // Markiere als Mock\n        return mockHealth.success;\n      } catch (mockError) {\n        console.error('Auch Mock-Backend fehlgeschlagen');\n        this.useBackend = false;\n        return false;\n      }\n    }\n  }\n\n  // Lade Fahrzeuge von automobile-nord.com\n  async loadVehiclesFromWebsite() {\n    try {\n      console.log('🚗 Lade Fahrzeuge von automobile-nord.com...');\n      this.isLoading = true;\n\n      // Prüfe Backend-Verfügbarkeit\n      const backendAvailable = await this.checkBackendHealth();\n      if (!backendAvailable) {\n        console.warn('Echtes Backend nicht verfügbar, verwende Mock-Backend');\n        const mockResult = await mockBackendService.scrapeAutomobileNord(true);\n        if (mockResult.success) {\n          this.vehicles = this.normalizeVehicleData(mockResult.data);\n          this.lastUpdate = new Date();\n          console.log(`✅ ${this.vehicles.length} Fahrzeuge vom Mock-Backend geladen`);\n          return this.vehicles;\n        }\n        return this.getFallbackVehicles();\n      }\n\n      // Scrape Fahrzeuge von der echten Website\n      const result = await apiService.scrapeAutomobileNord(true);\n      if (result.success && result.data) {\n        this.vehicles = this.normalizeVehicleData(result.data);\n        this.lastUpdate = new Date();\n\n        // Speichere in localStorage als Cache\n        localStorage.setItem('real_vehicles', JSON.stringify(this.vehicles));\n        localStorage.setItem('real_vehicles_timestamp', this.lastUpdate.toISOString());\n        console.log(`✅ ${this.vehicles.length} Fahrzeuge von automobile-nord.com geladen`);\n        return this.vehicles;\n      } else {\n        throw new Error('Keine Fahrzeugdaten erhalten');\n      }\n    } catch (error) {\n      console.error('❌ Fehler beim Laden von automobile-nord.com:', error);\n\n      // Versuche Cache zu laden\n      const cachedVehicles = this.loadFromCache();\n      if (cachedVehicles.length > 0) {\n        console.log('📦 Verwende gecachte Fahrzeugdaten');\n        this.vehicles = cachedVehicles;\n        return this.vehicles;\n      }\n\n      // Fallback zu Beispieldaten\n      console.log('🔄 Verwende Fallback-Daten');\n      return this.getFallbackVehicles();\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  // Lade zusätzliche Marktdaten von mobile.de\n  async enrichWithMarketData(searchQuery = '') {\n    try {\n      if (!this.useBackend) return;\n      console.log('📊 Lade Marktdaten von mobile.de...');\n      const marketData = await apiService.scrapeMobileDe(searchQuery);\n      if (marketData.success && marketData.data) {\n        var _marketData$data$mark;\n        console.log(`✅ ${((_marketData$data$mark = marketData.data.marketData) === null || _marketData$data$mark === void 0 ? void 0 : _marketData$data$mark.length) || 0} Marktdaten von mobile.de geladen`);\n        return marketData.data;\n      }\n    } catch (error) {\n      console.error('❌ Fehler beim Laden von mobile.de:', error);\n    }\n    return null;\n  }\n\n  // Normalisiere Fahrzeugdaten für einheitliche Struktur\n  normalizeVehicleData(vehicles) {\n    return vehicles.map((vehicle, index) => ({\n      id: vehicle.id || `real-${index + 1}`,\n      make: vehicle.make || vehicle.brand || 'Unbekannt',\n      model: vehicle.model || 'Unbekannt',\n      year: vehicle.year || new Date().getFullYear(),\n      price: vehicle.price || 0,\n      status: this.normalizeStatus(vehicle.status),\n      type: vehicle.type || vehicle.category || 'PKW',\n      fuelType: vehicle.fuelType || vehicle.fuel || 'Benzin',\n      mileage: vehicle.mileage || vehicle.km || 0,\n      color: vehicle.color || 'Unbekannt',\n      vin: vehicle.vin || `VIN${index.toString().padStart(9, '0')}`,\n      location: vehicle.location || `Stellplatz ${String.fromCharCode(65 + Math.floor(index / 10))}${index % 10 + 1}`,\n      smartcarConnected: vehicle.smartcarConnected || false,\n      battery: vehicle.battery || null,\n      image: vehicle.image || null,\n      description: vehicle.description || '',\n      features: vehicle.features || [],\n      marketAnalysis: vehicle.marketAnalysis || null,\n      createdAt: vehicle.createdAt || new Date().toISOString(),\n      updatedAt: vehicle.updatedAt || new Date().toISOString()\n    }));\n  }\n\n  // Normalisiere Status-Werte\n  normalizeStatus(status) {\n    if (!status) return 'verfügbar';\n    const statusMap = {\n      'In Inventory': 'verfügbar',\n      'In Transit': 'transport',\n      'In Inspection': 'prüfung',\n      'In Wash': 'wäsche',\n      'In Export': 'export',\n      'Maintenance': 'werkstatt',\n      'Sold': 'verkauft',\n      'verfügbar': 'verfügbar',\n      'verkauft': 'verkauft',\n      'reserviert': 'reserviert',\n      'werkstatt': 'werkstatt'\n    };\n    return statusMap[status] || 'verfügbar';\n  }\n\n  // Lade aus Cache\n  loadFromCache() {\n    try {\n      const cached = localStorage.getItem('real_vehicles');\n      const timestamp = localStorage.getItem('real_vehicles_timestamp');\n      if (cached && timestamp) {\n        const cacheAge = Date.now() - new Date(timestamp).getTime();\n        const maxAge = 30 * 60 * 1000; // 30 Minuten\n\n        if (cacheAge < maxAge) {\n          return JSON.parse(cached);\n        }\n      }\n    } catch (error) {\n      console.error('Fehler beim Laden aus Cache:', error);\n    }\n    return [];\n  }\n\n  // Fallback-Fahrzeuge wenn Backend nicht verfügbar\n  getFallbackVehicles() {\n    const fallbackData = [{\n      id: 'fb-001',\n      make: 'BMW',\n      model: '320i',\n      year: 2023,\n      price: 45000,\n      status: 'verfügbar',\n      type: 'Limousine',\n      fuelType: 'Benzin',\n      mileage: 12500,\n      color: 'Schwarz',\n      vin: 'WBA123456789',\n      location: 'Stellplatz A1'\n    }, {\n      id: 'fb-002',\n      make: 'Mercedes',\n      model: 'C-Klasse',\n      year: 2022,\n      price: 52000,\n      status: 'verkauft',\n      type: 'Limousine',\n      fuelType: 'Diesel',\n      mileage: 25000,\n      color: 'Silber',\n      vin: 'WDD987654321',\n      location: 'Stellplatz A2'\n    }, {\n      id: 'fb-003',\n      make: 'Audi',\n      model: 'A4',\n      year: 2023,\n      price: 48000,\n      status: 'reserviert',\n      type: 'Limousine',\n      fuelType: 'Benzin',\n      mileage: 8500,\n      color: 'Weiß',\n      vin: 'WAU456789123',\n      location: 'Stellplatz B1'\n    }];\n    this.vehicles = fallbackData;\n    return fallbackData;\n  }\n\n  // Alle Fahrzeuge abrufen\n  async getAllVehicles() {\n    if (this.vehicles.length === 0 || this.shouldRefresh()) {\n      await this.loadVehiclesFromWebsite();\n    }\n    return this.vehicles;\n  }\n\n  // Prüfe ob Daten aktualisiert werden sollten\n  shouldRefresh() {\n    if (!this.lastUpdate) return true;\n    const age = Date.now() - this.lastUpdate.getTime();\n    return age > 15 * 60 * 1000; // 15 Minuten\n  }\n\n  // Einzelnes Fahrzeug abrufen\n  getVehicleById(id) {\n    return this.vehicles.find(v => v.id === id);\n  }\n\n  // Fahrzeuge nach Status filtern\n  getVehiclesByStatus(status) {\n    return this.vehicles.filter(v => v.status === status);\n  }\n\n  // Fahrzeuge durchsuchen\n  searchVehicles(query) {\n    return apiService.searchVehicles(this.vehicles, query);\n  }\n\n  // Statistiken berechnen\n  getStatistics() {\n    return apiService.calculateStatistics(this.vehicles);\n  }\n\n  // Fahrzeug hinzufügen (über Backend)\n  async addVehicle(vehicle) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.createVehicle(vehicle);\n        if (result.success) {\n          this.vehicles.push(result.data);\n          return result.data;\n        }\n      }\n\n      // Fallback: lokales Hinzufügen\n      const newId = `local-${Date.now()}`;\n      const newVehicle = {\n        ...vehicle,\n        id: newId\n      };\n      this.vehicles.push(newVehicle);\n      return newVehicle;\n    } catch (error) {\n      console.error('Fehler beim Hinzufügen des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Fahrzeug aktualisieren\n  async updateVehicle(id, updates) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.updateVehicle(id, updates);\n        if (result.success) {\n          const index = this.vehicles.findIndex(v => v.id === id);\n          if (index !== -1) {\n            this.vehicles[index] = {\n              ...this.vehicles[index],\n              ...updates\n            };\n          }\n          return result.data;\n        }\n      }\n\n      // Fallback: lokale Aktualisierung\n      const index = this.vehicles.findIndex(v => v.id === id);\n      if (index !== -1) {\n        this.vehicles[index] = {\n          ...this.vehicles[index],\n          ...updates\n        };\n        return this.vehicles[index];\n      }\n      return null;\n    } catch (error) {\n      console.error('Fehler beim Aktualisieren des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Fahrzeug löschen\n  async deleteVehicle(id) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.deleteVehicle(id);\n        if (result.success) {\n          this.vehicles = this.vehicles.filter(v => v.id !== id);\n          return true;\n        }\n      }\n\n      // Fallback: lokales Löschen\n      this.vehicles = this.vehicles.filter(v => v.id !== id);\n      return true;\n    } catch (error) {\n      console.error('Fehler beim Löschen des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Daten manuell aktualisieren\n  async refreshData() {\n    this.lastUpdate = null;\n    return await this.loadVehiclesFromWebsite();\n  }\n}\nconst realVehicleService = new RealVehicleService();\nexport default realVehicleService;", "map": {"version": 3, "names": ["apiService", "mockBackendService", "RealVehicleService", "constructor", "vehicles", "isLoading", "lastUpdate", "useBackend", "checkBackendHealth", "health", "checkHealth", "status", "success", "error", "console", "warn", "mockHealth", "mockError", "loadVehiclesFromWebsite", "log", "backendAvailable", "mockResult", "scrapeAutomobileNord", "normalizeVehicleData", "data", "Date", "length", "getFallbackVehicles", "result", "localStorage", "setItem", "JSON", "stringify", "toISOString", "Error", "cachedVehicles", "loadFromCache", "enrichWithMarketData", "searchQuery", "marketData", "scrapeMobileDe", "_marketData$data$mark", "map", "vehicle", "index", "id", "make", "brand", "model", "year", "getFullYear", "price", "normalizeStatus", "type", "category", "fuelType", "fuel", "mileage", "km", "color", "vin", "toString", "padStart", "location", "String", "fromCharCode", "Math", "floor", "smartcarConnected", "battery", "image", "description", "features", "marketAnalysis", "createdAt", "updatedAt", "statusMap", "cached", "getItem", "timestamp", "cacheAge", "now", "getTime", "maxAge", "parse", "fallbackD<PERSON>", "getAllVehicles", "shouldRefresh", "age", "getVehicleById", "find", "v", "getVehiclesByStatus", "filter", "searchVehicles", "query", "getStatistics", "calculateStatistics", "addVehicle", "createVehicle", "push", "newId", "newVehicle", "updateVehicle", "updates", "findIndex", "deleteVehicle", "refreshData", "realVehicleService"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/services/realVehicleService.js"], "sourcesContent": ["// Echter Fahrzeug-Service für Daten von automobile-nord.com und mobile.de\nimport apiService from './apiService';\nimport mockBackendService from './mockBackendService';\n\nclass RealVehicleService {\n  constructor() {\n    this.vehicles = [];\n    this.isLoading = false;\n    this.lastUpdate = null;\n    this.useBackend = true;\n  }\n\n  // Prüfe Backend-Verfügbarkeit\n  async checkBackendHealth() {\n    try {\n      const health = await apiService.checkHealth();\n      this.useBackend = health.status === 'ok' || health.success;\n      return this.useBackend;\n    } catch (error) {\n      console.warn('Echtes Backend nicht erreichbar, verwende Mock-Backend');\n      // Verwende Mock-Backend als Fallback\n      try {\n        const mockHealth = await mockBackendService.checkHealth();\n        this.useBackend = false; // Markiere als Mock\n        return mockHealth.success;\n      } catch (mockError) {\n        console.error('Auch Mock-Backend fehlgeschlagen');\n        this.useBackend = false;\n        return false;\n      }\n    }\n  }\n\n  // Lade Fahrzeuge von automobile-nord.com\n  async loadVehiclesFromWebsite() {\n    try {\n      console.log('🚗 Lade Fahrzeuge von automobile-nord.com...');\n      this.isLoading = true;\n\n      // Prüfe Backend-Verfügbarkeit\n      const backendAvailable = await this.checkBackendHealth();\n      \n      if (!backendAvailable) {\n        console.warn('Echtes Backend nicht verfügbar, verwende Mock-Backend');\n        const mockResult = await mockBackendService.scrapeAutomobileNord(true);\n        if (mockResult.success) {\n          this.vehicles = this.normalizeVehicleData(mockResult.data);\n          this.lastUpdate = new Date();\n          console.log(`✅ ${this.vehicles.length} Fahrzeuge vom Mock-Backend geladen`);\n          return this.vehicles;\n        }\n        return this.getFallbackVehicles();\n      }\n\n      // Scrape Fahrzeuge von der echten Website\n      const result = await apiService.scrapeAutomobileNord(true);\n      \n      if (result.success && result.data) {\n        this.vehicles = this.normalizeVehicleData(result.data);\n        this.lastUpdate = new Date();\n        \n        // Speichere in localStorage als Cache\n        localStorage.setItem('real_vehicles', JSON.stringify(this.vehicles));\n        localStorage.setItem('real_vehicles_timestamp', this.lastUpdate.toISOString());\n        \n        console.log(`✅ ${this.vehicles.length} Fahrzeuge von automobile-nord.com geladen`);\n        return this.vehicles;\n      } else {\n        throw new Error('Keine Fahrzeugdaten erhalten');\n      }\n    } catch (error) {\n      console.error('❌ Fehler beim Laden von automobile-nord.com:', error);\n      \n      // Versuche Cache zu laden\n      const cachedVehicles = this.loadFromCache();\n      if (cachedVehicles.length > 0) {\n        console.log('📦 Verwende gecachte Fahrzeugdaten');\n        this.vehicles = cachedVehicles;\n        return this.vehicles;\n      }\n      \n      // Fallback zu Beispieldaten\n      console.log('🔄 Verwende Fallback-Daten');\n      return this.getFallbackVehicles();\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  // Lade zusätzliche Marktdaten von mobile.de\n  async enrichWithMarketData(searchQuery = '') {\n    try {\n      if (!this.useBackend) return;\n\n      console.log('📊 Lade Marktdaten von mobile.de...');\n      const marketData = await apiService.scrapeMobileDe(searchQuery);\n      \n      if (marketData.success && marketData.data) {\n        console.log(`✅ ${marketData.data.marketData?.length || 0} Marktdaten von mobile.de geladen`);\n        return marketData.data;\n      }\n    } catch (error) {\n      console.error('❌ Fehler beim Laden von mobile.de:', error);\n    }\n    return null;\n  }\n\n  // Normalisiere Fahrzeugdaten für einheitliche Struktur\n  normalizeVehicleData(vehicles) {\n    return vehicles.map((vehicle, index) => ({\n      id: vehicle.id || `real-${index + 1}`,\n      make: vehicle.make || vehicle.brand || 'Unbekannt',\n      model: vehicle.model || 'Unbekannt',\n      year: vehicle.year || new Date().getFullYear(),\n      price: vehicle.price || 0,\n      status: this.normalizeStatus(vehicle.status),\n      type: vehicle.type || vehicle.category || 'PKW',\n      fuelType: vehicle.fuelType || vehicle.fuel || 'Benzin',\n      mileage: vehicle.mileage || vehicle.km || 0,\n      color: vehicle.color || 'Unbekannt',\n      vin: vehicle.vin || `VIN${index.toString().padStart(9, '0')}`,\n      location: vehicle.location || `Stellplatz ${String.fromCharCode(65 + Math.floor(index/10))}${(index%10)+1}`,\n      smartcarConnected: vehicle.smartcarConnected || false,\n      battery: vehicle.battery || null,\n      image: vehicle.image || null,\n      description: vehicle.description || '',\n      features: vehicle.features || [],\n      marketAnalysis: vehicle.marketAnalysis || null,\n      createdAt: vehicle.createdAt || new Date().toISOString(),\n      updatedAt: vehicle.updatedAt || new Date().toISOString()\n    }));\n  }\n\n  // Normalisiere Status-Werte\n  normalizeStatus(status) {\n    if (!status) return 'verfügbar';\n    \n    const statusMap = {\n      'In Inventory': 'verfügbar',\n      'In Transit': 'transport',\n      'In Inspection': 'prüfung',\n      'In Wash': 'wäsche',\n      'In Export': 'export',\n      'Maintenance': 'werkstatt',\n      'Sold': 'verkauft',\n      'verfügbar': 'verfügbar',\n      'verkauft': 'verkauft',\n      'reserviert': 'reserviert',\n      'werkstatt': 'werkstatt'\n    };\n    \n    return statusMap[status] || 'verfügbar';\n  }\n\n  // Lade aus Cache\n  loadFromCache() {\n    try {\n      const cached = localStorage.getItem('real_vehicles');\n      const timestamp = localStorage.getItem('real_vehicles_timestamp');\n      \n      if (cached && timestamp) {\n        const cacheAge = Date.now() - new Date(timestamp).getTime();\n        const maxAge = 30 * 60 * 1000; // 30 Minuten\n        \n        if (cacheAge < maxAge) {\n          return JSON.parse(cached);\n        }\n      }\n    } catch (error) {\n      console.error('Fehler beim Laden aus Cache:', error);\n    }\n    return [];\n  }\n\n  // Fallback-Fahrzeuge wenn Backend nicht verfügbar\n  getFallbackVehicles() {\n    const fallbackData = [\n      { id: 'fb-001', make: 'BMW', model: '320i', year: 2023, price: 45000, status: 'verfügbar', type: 'Limousine', fuelType: 'Benzin', mileage: 12500, color: 'Schwarz', vin: 'WBA123456789', location: 'Stellplatz A1' },\n      { id: 'fb-002', make: 'Mercedes', model: 'C-Klasse', year: 2022, price: 52000, status: 'verkauft', type: 'Limousine', fuelType: 'Diesel', mileage: 25000, color: 'Silber', vin: 'WDD987654321', location: 'Stellplatz A2' },\n      { id: 'fb-003', make: 'Audi', model: 'A4', year: 2023, price: 48000, status: 'reserviert', type: 'Limousine', fuelType: 'Benzin', mileage: 8500, color: 'Weiß', vin: 'WAU456789123', location: 'Stellplatz B1' },\n    ];\n    \n    this.vehicles = fallbackData;\n    return fallbackData;\n  }\n\n  // Alle Fahrzeuge abrufen\n  async getAllVehicles() {\n    if (this.vehicles.length === 0 || this.shouldRefresh()) {\n      await this.loadVehiclesFromWebsite();\n    }\n    return this.vehicles;\n  }\n\n  // Prüfe ob Daten aktualisiert werden sollten\n  shouldRefresh() {\n    if (!this.lastUpdate) return true;\n    const age = Date.now() - this.lastUpdate.getTime();\n    return age > 15 * 60 * 1000; // 15 Minuten\n  }\n\n  // Einzelnes Fahrzeug abrufen\n  getVehicleById(id) {\n    return this.vehicles.find(v => v.id === id);\n  }\n\n  // Fahrzeuge nach Status filtern\n  getVehiclesByStatus(status) {\n    return this.vehicles.filter(v => v.status === status);\n  }\n\n  // Fahrzeuge durchsuchen\n  searchVehicles(query) {\n    return apiService.searchVehicles(this.vehicles, query);\n  }\n\n  // Statistiken berechnen\n  getStatistics() {\n    return apiService.calculateStatistics(this.vehicles);\n  }\n\n  // Fahrzeug hinzufügen (über Backend)\n  async addVehicle(vehicle) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.createVehicle(vehicle);\n        if (result.success) {\n          this.vehicles.push(result.data);\n          return result.data;\n        }\n      }\n      \n      // Fallback: lokales Hinzufügen\n      const newId = `local-${Date.now()}`;\n      const newVehicle = { ...vehicle, id: newId };\n      this.vehicles.push(newVehicle);\n      return newVehicle;\n    } catch (error) {\n      console.error('Fehler beim Hinzufügen des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Fahrzeug aktualisieren\n  async updateVehicle(id, updates) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.updateVehicle(id, updates);\n        if (result.success) {\n          const index = this.vehicles.findIndex(v => v.id === id);\n          if (index !== -1) {\n            this.vehicles[index] = { ...this.vehicles[index], ...updates };\n          }\n          return result.data;\n        }\n      }\n      \n      // Fallback: lokale Aktualisierung\n      const index = this.vehicles.findIndex(v => v.id === id);\n      if (index !== -1) {\n        this.vehicles[index] = { ...this.vehicles[index], ...updates };\n        return this.vehicles[index];\n      }\n      return null;\n    } catch (error) {\n      console.error('Fehler beim Aktualisieren des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Fahrzeug löschen\n  async deleteVehicle(id) {\n    try {\n      if (this.useBackend) {\n        const result = await apiService.deleteVehicle(id);\n        if (result.success) {\n          this.vehicles = this.vehicles.filter(v => v.id !== id);\n          return true;\n        }\n      }\n      \n      // Fallback: lokales Löschen\n      this.vehicles = this.vehicles.filter(v => v.id !== id);\n      return true;\n    } catch (error) {\n      console.error('Fehler beim Löschen des Fahrzeugs:', error);\n      throw error;\n    }\n  }\n\n  // Daten manuell aktualisieren\n  async refreshData() {\n    this.lastUpdate = null;\n    return await this.loadVehiclesFromWebsite();\n  }\n}\n\nconst realVehicleService = new RealVehicleService();\nexport default realVehicleService;\n"], "mappings": "AAAA;AACA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,kBAAkB,MAAM,sBAAsB;AAErD,MAAMC,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,GAAG,IAAI;EACxB;;EAEA;EACA,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMT,UAAU,CAACU,WAAW,CAAC,CAAC;MAC7C,IAAI,CAACH,UAAU,GAAGE,MAAM,CAACE,MAAM,KAAK,IAAI,IAAIF,MAAM,CAACG,OAAO;MAC1D,OAAO,IAAI,CAACL,UAAU;IACxB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,wDAAwD,CAAC;MACtE;MACA,IAAI;QACF,MAAMC,UAAU,GAAG,MAAMf,kBAAkB,CAACS,WAAW,CAAC,CAAC;QACzD,IAAI,CAACH,UAAU,GAAG,KAAK,CAAC,CAAC;QACzB,OAAOS,UAAU,CAACJ,OAAO;MAC3B,CAAC,CAAC,OAAOK,SAAS,EAAE;QAClBH,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAC;QACjD,IAAI,CAACN,UAAU,GAAG,KAAK;QACvB,OAAO,KAAK;MACd;IACF;EACF;;EAEA;EACA,MAAMW,uBAAuBA,CAAA,EAAG;IAC9B,IAAI;MACFJ,OAAO,CAACK,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACd,SAAS,GAAG,IAAI;;MAErB;MACA,MAAMe,gBAAgB,GAAG,MAAM,IAAI,CAACZ,kBAAkB,CAAC,CAAC;MAExD,IAAI,CAACY,gBAAgB,EAAE;QACrBN,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;QACrE,MAAMM,UAAU,GAAG,MAAMpB,kBAAkB,CAACqB,oBAAoB,CAAC,IAAI,CAAC;QACtE,IAAID,UAAU,CAACT,OAAO,EAAE;UACtB,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACmB,oBAAoB,CAACF,UAAU,CAACG,IAAI,CAAC;UAC1D,IAAI,CAAClB,UAAU,GAAG,IAAImB,IAAI,CAAC,CAAC;UAC5BX,OAAO,CAACK,GAAG,CAAC,KAAK,IAAI,CAACf,QAAQ,CAACsB,MAAM,qCAAqC,CAAC;UAC3E,OAAO,IAAI,CAACtB,QAAQ;QACtB;QACA,OAAO,IAAI,CAACuB,mBAAmB,CAAC,CAAC;MACnC;;MAEA;MACA,MAAMC,MAAM,GAAG,MAAM5B,UAAU,CAACsB,oBAAoB,CAAC,IAAI,CAAC;MAE1D,IAAIM,MAAM,CAAChB,OAAO,IAAIgB,MAAM,CAACJ,IAAI,EAAE;QACjC,IAAI,CAACpB,QAAQ,GAAG,IAAI,CAACmB,oBAAoB,CAACK,MAAM,CAACJ,IAAI,CAAC;QACtD,IAAI,CAAClB,UAAU,GAAG,IAAImB,IAAI,CAAC,CAAC;;QAE5B;QACAI,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC5B,QAAQ,CAAC,CAAC;QACpEyB,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAACxB,UAAU,CAAC2B,WAAW,CAAC,CAAC,CAAC;QAE9EnB,OAAO,CAACK,GAAG,CAAC,KAAK,IAAI,CAACf,QAAQ,CAACsB,MAAM,4CAA4C,CAAC;QAClF,OAAO,IAAI,CAACtB,QAAQ;MACtB,CAAC,MAAM;QACL,MAAM,IAAI8B,KAAK,CAAC,8BAA8B,CAAC;MACjD;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;;MAEpE;MACA,MAAMsB,cAAc,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MAC3C,IAAID,cAAc,CAACT,MAAM,GAAG,CAAC,EAAE;QAC7BZ,OAAO,CAACK,GAAG,CAAC,oCAAoC,CAAC;QACjD,IAAI,CAACf,QAAQ,GAAG+B,cAAc;QAC9B,OAAO,IAAI,CAAC/B,QAAQ;MACtB;;MAEA;MACAU,OAAO,CAACK,GAAG,CAAC,4BAA4B,CAAC;MACzC,OAAO,IAAI,CAACQ,mBAAmB,CAAC,CAAC;IACnC,CAAC,SAAS;MACR,IAAI,CAACtB,SAAS,GAAG,KAAK;IACxB;EACF;;EAEA;EACA,MAAMgC,oBAAoBA,CAACC,WAAW,GAAG,EAAE,EAAE;IAC3C,IAAI;MACF,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAAE;MAEtBO,OAAO,CAACK,GAAG,CAAC,qCAAqC,CAAC;MAClD,MAAMoB,UAAU,GAAG,MAAMvC,UAAU,CAACwC,cAAc,CAACF,WAAW,CAAC;MAE/D,IAAIC,UAAU,CAAC3B,OAAO,IAAI2B,UAAU,CAACf,IAAI,EAAE;QAAA,IAAAiB,qBAAA;QACzC3B,OAAO,CAACK,GAAG,CAAC,KAAK,EAAAsB,qBAAA,GAAAF,UAAU,CAACf,IAAI,CAACe,UAAU,cAAAE,qBAAA,uBAA1BA,qBAAA,CAA4Bf,MAAM,KAAI,CAAC,mCAAmC,CAAC;QAC5F,OAAOa,UAAU,CAACf,IAAI;MACxB;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;IACA,OAAO,IAAI;EACb;;EAEA;EACAU,oBAAoBA,CAACnB,QAAQ,EAAE;IAC7B,OAAOA,QAAQ,CAACsC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;MACvCC,EAAE,EAAEF,OAAO,CAACE,EAAE,IAAI,QAAQD,KAAK,GAAG,CAAC,EAAE;MACrCE,IAAI,EAAEH,OAAO,CAACG,IAAI,IAAIH,OAAO,CAACI,KAAK,IAAI,WAAW;MAClDC,KAAK,EAAEL,OAAO,CAACK,KAAK,IAAI,WAAW;MACnCC,IAAI,EAAEN,OAAO,CAACM,IAAI,IAAI,IAAIxB,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;MAC9CC,KAAK,EAAER,OAAO,CAACQ,KAAK,IAAI,CAAC;MACzBxC,MAAM,EAAE,IAAI,CAACyC,eAAe,CAACT,OAAO,CAAChC,MAAM,CAAC;MAC5C0C,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAIV,OAAO,CAACW,QAAQ,IAAI,KAAK;MAC/CC,QAAQ,EAAEZ,OAAO,CAACY,QAAQ,IAAIZ,OAAO,CAACa,IAAI,IAAI,QAAQ;MACtDC,OAAO,EAAEd,OAAO,CAACc,OAAO,IAAId,OAAO,CAACe,EAAE,IAAI,CAAC;MAC3CC,KAAK,EAAEhB,OAAO,CAACgB,KAAK,IAAI,WAAW;MACnCC,GAAG,EAAEjB,OAAO,CAACiB,GAAG,IAAI,MAAMhB,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;MAC7DC,QAAQ,EAAEpB,OAAO,CAACoB,QAAQ,IAAI,cAAcC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACvB,KAAK,GAAC,EAAE,CAAC,CAAC,GAAIA,KAAK,GAAC,EAAE,GAAE,CAAC,EAAE;MAC3GwB,iBAAiB,EAAEzB,OAAO,CAACyB,iBAAiB,IAAI,KAAK;MACrDC,OAAO,EAAE1B,OAAO,CAAC0B,OAAO,IAAI,IAAI;MAChCC,KAAK,EAAE3B,OAAO,CAAC2B,KAAK,IAAI,IAAI;MAC5BC,WAAW,EAAE5B,OAAO,CAAC4B,WAAW,IAAI,EAAE;MACtCC,QAAQ,EAAE7B,OAAO,CAAC6B,QAAQ,IAAI,EAAE;MAChCC,cAAc,EAAE9B,OAAO,CAAC8B,cAAc,IAAI,IAAI;MAC9CC,SAAS,EAAE/B,OAAO,CAAC+B,SAAS,IAAI,IAAIjD,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;MACxD0C,SAAS,EAAEhC,OAAO,CAACgC,SAAS,IAAI,IAAIlD,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC;IACzD,CAAC,CAAC,CAAC;EACL;;EAEA;EACAmB,eAAeA,CAACzC,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,EAAE,OAAO,WAAW;IAE/B,MAAMiE,SAAS,GAAG;MAChB,cAAc,EAAE,WAAW;MAC3B,YAAY,EAAE,WAAW;MACzB,eAAe,EAAE,SAAS;MAC1B,SAAS,EAAE,QAAQ;MACnB,WAAW,EAAE,QAAQ;MACrB,aAAa,EAAE,WAAW;MAC1B,MAAM,EAAE,UAAU;MAClB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,YAAY,EAAE,YAAY;MAC1B,WAAW,EAAE;IACf,CAAC;IAED,OAAOA,SAAS,CAACjE,MAAM,CAAC,IAAI,WAAW;EACzC;;EAEA;EACAyB,aAAaA,CAAA,EAAG;IACd,IAAI;MACF,MAAMyC,MAAM,GAAGhD,YAAY,CAACiD,OAAO,CAAC,eAAe,CAAC;MACpD,MAAMC,SAAS,GAAGlD,YAAY,CAACiD,OAAO,CAAC,yBAAyB,CAAC;MAEjE,IAAID,MAAM,IAAIE,SAAS,EAAE;QACvB,MAAMC,QAAQ,GAAGvD,IAAI,CAACwD,GAAG,CAAC,CAAC,GAAG,IAAIxD,IAAI,CAACsD,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC;QAC3D,MAAMC,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;QAE/B,IAAIH,QAAQ,GAAGG,MAAM,EAAE;UACrB,OAAOpD,IAAI,CAACqD,KAAK,CAACP,MAAM,CAAC;QAC3B;MACF;IACF,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;IACA,OAAO,EAAE;EACX;;EAEA;EACAc,mBAAmBA,CAAA,EAAG;IACpB,MAAM0D,YAAY,GAAG,CACnB;MAAExC,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,KAAK;MAAEE,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEE,KAAK,EAAE,KAAK;MAAExC,MAAM,EAAE,WAAW;MAAE0C,IAAI,EAAE,WAAW;MAAEE,QAAQ,EAAE,QAAQ;MAAEE,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAE,SAAS;MAAEC,GAAG,EAAE,cAAc;MAAEG,QAAQ,EAAE;IAAgB,CAAC,EACpN;MAAElB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,UAAU;MAAEE,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEE,KAAK,EAAE,KAAK;MAAExC,MAAM,EAAE,UAAU;MAAE0C,IAAI,EAAE,WAAW;MAAEE,QAAQ,EAAE,QAAQ;MAAEE,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAE,QAAQ;MAAEC,GAAG,EAAE,cAAc;MAAEG,QAAQ,EAAE;IAAgB,CAAC,EAC3N;MAAElB,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEE,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAEE,KAAK,EAAE,KAAK;MAAExC,MAAM,EAAE,YAAY;MAAE0C,IAAI,EAAE,WAAW;MAAEE,QAAQ,EAAE,QAAQ;MAAEE,OAAO,EAAE,IAAI;MAAEE,KAAK,EAAE,MAAM;MAAEC,GAAG,EAAE,cAAc;MAAEG,QAAQ,EAAE;IAAgB,CAAC,CACjN;IAED,IAAI,CAAC3D,QAAQ,GAAGiF,YAAY;IAC5B,OAAOA,YAAY;EACrB;;EAEA;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAClF,QAAQ,CAACsB,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC6D,aAAa,CAAC,CAAC,EAAE;MACtD,MAAM,IAAI,CAACrE,uBAAuB,CAAC,CAAC;IACtC;IACA,OAAO,IAAI,CAACd,QAAQ;EACtB;;EAEA;EACAmF,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACjF,UAAU,EAAE,OAAO,IAAI;IACjC,MAAMkF,GAAG,GAAG/D,IAAI,CAACwD,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC3E,UAAU,CAAC4E,OAAO,CAAC,CAAC;IAClD,OAAOM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC/B;;EAEA;EACAC,cAAcA,CAAC5C,EAAE,EAAE;IACjB,OAAO,IAAI,CAACzC,QAAQ,CAACsF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC;EAC7C;;EAEA;EACA+C,mBAAmBA,CAACjF,MAAM,EAAE;IAC1B,OAAO,IAAI,CAACP,QAAQ,CAACyF,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAChF,MAAM,KAAKA,MAAM,CAAC;EACvD;;EAEA;EACAmF,cAAcA,CAACC,KAAK,EAAE;IACpB,OAAO/F,UAAU,CAAC8F,cAAc,CAAC,IAAI,CAAC1F,QAAQ,EAAE2F,KAAK,CAAC;EACxD;;EAEA;EACAC,aAAaA,CAAA,EAAG;IACd,OAAOhG,UAAU,CAACiG,mBAAmB,CAAC,IAAI,CAAC7F,QAAQ,CAAC;EACtD;;EAEA;EACA,MAAM8F,UAAUA,CAACvD,OAAO,EAAE;IACxB,IAAI;MACF,IAAI,IAAI,CAACpC,UAAU,EAAE;QACnB,MAAMqB,MAAM,GAAG,MAAM5B,UAAU,CAACmG,aAAa,CAACxD,OAAO,CAAC;QACtD,IAAIf,MAAM,CAAChB,OAAO,EAAE;UAClB,IAAI,CAACR,QAAQ,CAACgG,IAAI,CAACxE,MAAM,CAACJ,IAAI,CAAC;UAC/B,OAAOI,MAAM,CAACJ,IAAI;QACpB;MACF;;MAEA;MACA,MAAM6E,KAAK,GAAG,SAAS5E,IAAI,CAACwD,GAAG,CAAC,CAAC,EAAE;MACnC,MAAMqB,UAAU,GAAG;QAAE,GAAG3D,OAAO;QAAEE,EAAE,EAAEwD;MAAM,CAAC;MAC5C,IAAI,CAACjG,QAAQ,CAACgG,IAAI,CAACE,UAAU,CAAC;MAC9B,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM0F,aAAaA,CAAC1D,EAAE,EAAE2D,OAAO,EAAE;IAC/B,IAAI;MACF,IAAI,IAAI,CAACjG,UAAU,EAAE;QACnB,MAAMqB,MAAM,GAAG,MAAM5B,UAAU,CAACuG,aAAa,CAAC1D,EAAE,EAAE2D,OAAO,CAAC;QAC1D,IAAI5E,MAAM,CAAChB,OAAO,EAAE;UAClB,MAAMgC,KAAK,GAAG,IAAI,CAACxC,QAAQ,CAACqG,SAAS,CAACd,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC;UACvD,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACxC,QAAQ,CAACwC,KAAK,CAAC,GAAG;cAAE,GAAG,IAAI,CAACxC,QAAQ,CAACwC,KAAK,CAAC;cAAE,GAAG4D;YAAQ,CAAC;UAChE;UACA,OAAO5E,MAAM,CAACJ,IAAI;QACpB;MACF;;MAEA;MACA,MAAMoB,KAAK,GAAG,IAAI,CAACxC,QAAQ,CAACqG,SAAS,CAACd,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC;MACvD,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACxC,QAAQ,CAACwC,KAAK,CAAC,GAAG;UAAE,GAAG,IAAI,CAACxC,QAAQ,CAACwC,KAAK,CAAC;UAAE,GAAG4D;QAAQ,CAAC;QAC9D,OAAO,IAAI,CAACpG,QAAQ,CAACwC,KAAK,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM6F,aAAaA,CAAC7D,EAAE,EAAE;IACtB,IAAI;MACF,IAAI,IAAI,CAACtC,UAAU,EAAE;QACnB,MAAMqB,MAAM,GAAG,MAAM5B,UAAU,CAAC0G,aAAa,CAAC7D,EAAE,CAAC;QACjD,IAAIjB,MAAM,CAAChB,OAAO,EAAE;UAClB,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyF,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC;UACtD,OAAO,IAAI;QACb;MACF;;MAEA;MACA,IAAI,CAACzC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACyF,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC;MACtD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM8F,WAAWA,CAAA,EAAG;IAClB,IAAI,CAACrG,UAAU,GAAG,IAAI;IACtB,OAAO,MAAM,IAAI,CAACY,uBAAuB,CAAC,CAAC;EAC7C;AACF;AAEA,MAAM0F,kBAAkB,GAAG,IAAI1G,kBAAkB,CAAC,CAAC;AACnD,eAAe0G,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}