import React, { useState, useEffect } from 'react';
import { FaCar, FaWrench, FaSprayCan, FaShippingFast, FaCheckCircle } from 'react-icons/fa';
import vehicleService from '../services/vehicleService';

const STATUS_ZONES = {
  verfügbar: { name: 'Verfügbar', icon: FaCheckCircle, color: 'bg-green-100 border-green-300', textColor: 'text-green-800' },
  werkstatt: { name: 'Werkstatt', icon: FaWrench, color: 'bg-red-100 border-red-300', textColor: 'text-red-800' },
  wäsche: { name: 'Wäsche', icon: FaSprayCan, color: 'bg-blue-100 border-blue-300', textColor: 'text-blue-800' },
  export: { name: 'Export', icon: FaShippingFast, color: 'bg-purple-100 border-purple-300', textColor: 'text-purple-800' },
  verkauft: { name: 'Verkauft', icon: FaCheckCircle, color: 'bg-gray-100 border-gray-300', textColor: 'text-gray-800' },
  reserviert: { name: 'Reserviert', icon: FaCar, color: 'bg-yellow-100 border-yellow-300', textColor: 'text-yellow-800' }
};

function DragDropDashboard() {
  const [vehicles, setVehicles] = useState([]);
  const [draggedVehicle, setDraggedVehicle] = useState(null);

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = () => {
    const allVehicles = vehicleService.getAllVehicles();
    setVehicles(allVehicles);
  };

  const handleDragStart = (e, vehicle) => {
    setDraggedVehicle(vehicle);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e, newStatus) => {
    e.preventDefault();
    
    if (draggedVehicle && draggedVehicle.status !== newStatus) {
      // Update vehicle status
      vehicleService.updateVehicle(draggedVehicle.id, { status: newStatus });
      
      // Reload vehicles to reflect changes
      loadVehicles();
      
      console.log(`Fahrzeug ${draggedVehicle.make} ${draggedVehicle.model} von ${draggedVehicle.status} zu ${newStatus} verschoben`);
    }
    
    setDraggedVehicle(null);
  };

  const getVehiclesByStatus = (status) => {
    return vehicles.filter(v => v.status === status);
  };

  const VehicleCard = ({ vehicle }) => (
    <div
      draggable
      onDragStart={(e) => handleDragStart(e, vehicle)}
      className="bg-white p-3 rounded-lg shadow-sm border border-gray-200 cursor-move hover:shadow-md transition-shadow mb-2"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FaCar className="text-blue-500 mr-2" size={16} />
          <div>
            <div className="font-medium text-sm">{vehicle.make}</div>
            <div className="text-xs text-gray-500">{vehicle.model} ({vehicle.year})</div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs font-bold">{vehicle.price?.toLocaleString('de-DE')} €</div>
          <div className="text-xs text-gray-500">{vehicle.km?.toLocaleString('de-DE')} km</div>
        </div>
      </div>
      {vehicle.smartcarConnected && (
        <div className="mt-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
          🔋 Smartcar verbunden
        </div>
      )}
    </div>
  );

  const StatusZone = ({ status, statusInfo }) => {
    const vehiclesInZone = getVehiclesByStatus(status);
    const Icon = statusInfo.icon;

    return (
      <div
        className={`${statusInfo.color} border-2 border-dashed rounded-lg p-4 min-h-[300px] transition-all hover:border-solid`}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, status)}
      >
        <div className="flex items-center mb-4">
          <Icon className={`${statusInfo.textColor} mr-2`} size={20} />
          <h3 className={`font-semibold ${statusInfo.textColor}`}>
            {statusInfo.name} ({vehiclesInZone.length})
          </h3>
        </div>
        
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {vehiclesInZone.map(vehicle => (
            <VehicleCard key={vehicle.id} vehicle={vehicle} />
          ))}
          
          {vehiclesInZone.length === 0 && (
            <div className={`text-center py-8 ${statusInfo.textColor} opacity-50`}>
              <Icon size={32} className="mx-auto mb-2" />
              <p className="text-sm">Keine Fahrzeuge</p>
              <p className="text-xs">Fahrzeuge hierher ziehen</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">🚗 Fahrzeug-Management</h1>
        <p className="text-gray-600">
          Ziehen Sie Fahrzeuge zwischen den Bereichen, um deren Status zu ändern
        </p>
      </div>

      {/* Statistiken */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
        {Object.entries(STATUS_ZONES).map(([status, info]) => {
          const count = getVehiclesByStatus(status).length;
          const Icon = info.icon;
          return (
            <div key={status} className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="flex items-center">
                <Icon className={info.textColor} size={20} />
                <div className="ml-3">
                  <div className="text-2xl font-bold">{count}</div>
                  <div className="text-sm text-gray-500">{info.name}</div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Drag & Drop Bereiche */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(STATUS_ZONES).map(([status, statusInfo]) => (
          <StatusZone key={status} status={status} statusInfo={statusInfo} />
        ))}
      </div>

      {/* Hinweis */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Drag & Drop Funktionalität</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>• Fahrzeuge per Drag & Drop zwischen Bereichen verschieben</p>
              <p>• Änderungen werden automatisch gespeichert</p>
              <p>• Smartcar-verbundene Fahrzeuge sind markiert</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DragDropDashboard;