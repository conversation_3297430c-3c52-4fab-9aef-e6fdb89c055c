import React, { useState, useEffect } from 'react';
import { FaCar, FaWrench, FaSprayCan, FaShippingFast, FaCheckCircle, FaSync, FaGlobe, FaDownload } from 'react-icons/fa';
import realVehicleService from '../services/realVehicleService';

const STATUS_ZONES = {
  verfügbar: { name: 'Verfügbar', icon: FaCheckCircle, color: 'bg-green-100 border-green-300', textColor: 'text-green-800' },
  werkstatt: { name: 'Werkstatt', icon: FaWrench, color: 'bg-red-100 border-red-300', textColor: 'text-red-800' },
  wäsche: { name: 'Wäsche', icon: FaSprayCan, color: 'bg-blue-100 border-blue-300', textColor: 'text-blue-800' },
  export: { name: 'Export', icon: FaShippingFast, color: 'bg-purple-100 border-purple-300', textColor: 'text-purple-800' },
  verkauft: { name: 'Verkauft', icon: FaCheckCircle, color: 'bg-gray-100 border-gray-300', textColor: 'text-gray-800' },
  reserviert: { name: 'Reserviert', icon: FaCar, color: 'bg-yellow-100 border-yellow-300', textColor: 'text-yellow-800' }
};

function DragDropDashboard() {
  const [vehicles, setVehicles] = useState([]);
  const [draggedVehicle, setDraggedVehicle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [scrapingFromWebsite, setScrapingFromWebsite] = useState(false);
  const [dataSource, setDataSource] = useState('loading...');

  useEffect(() => {
    loadVehicles();
  }, []);

  const loadVehicles = async () => {
    try {
      setLoading(true);
      console.log('🚗 Lade Fahrzeuge für Drag & Drop...');

      const allVehicles = await realVehicleService.getAllVehicles();
      setVehicles(allVehicles);

      // Bestimme Datenquelle
      if (allVehicles.length > 0) {
        if (allVehicles[0].id?.startsWith('fb-')) {
          setDataSource('Fallback-Daten');
        } else if (allVehicles[0].id?.startsWith('an-')) {
          setDataSource('automobile-nord.com (simuliert)');
        } else if (allVehicles[0].id?.startsWith('real-')) {
          setDataSource('automobile-nord.com (echt)');
        } else {
          setDataSource('Backend API');
        }
      } else {
        setDataSource('Keine Daten verfügbar');
      }

      console.log(`✅ ${allVehicles.length} Fahrzeuge geladen`);
    } catch (error) {
      console.error('❌ Fehler beim Laden der Fahrzeuge:', error);
      setDataSource('Fehler beim Laden');
    } finally {
      setLoading(false);
    }
  };

  const handleDragStart = (e, vehicle) => {
    setDraggedVehicle(vehicle);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e, newStatus) => {
    e.preventDefault();

    if (draggedVehicle && draggedVehicle.status !== newStatus) {
      try {
        // Update vehicle status
        await realVehicleService.updateVehicle(draggedVehicle.id, { status: newStatus });

        // Reload vehicles to reflect changes
        await loadVehicles();

        console.log(`✅ Fahrzeug ${draggedVehicle.make} ${draggedVehicle.model} von ${draggedVehicle.status} zu ${newStatus} verschoben`);
      } catch (error) {
        console.error('❌ Fehler beim Aktualisieren des Fahrzeugstatus:', error);
      }
    }

    setDraggedVehicle(null);
  };

  // Daten manuell aktualisieren
  const refreshData = async () => {
    try {
      setRefreshing(true);
      console.log('🔄 Aktualisiere Fahrzeugdaten...');

      const allVehicles = await realVehicleService.refreshData();
      setVehicles(allVehicles);

      console.log(`✅ ${allVehicles.length} Fahrzeuge aktualisiert`);
    } catch (error) {
      console.error('❌ Fehler beim Aktualisieren:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Fahrzeuge direkt von der Website laden
  const loadFromWebsite = async () => {
    try {
      setScrapingFromWebsite(true);
      console.log('🌐 Lade Fahrzeuge direkt von automobile-nord.com...');

      const allVehicles = await realVehicleService.loadVehiclesFromWebsite();
      setVehicles(allVehicles);
      setDataSource('automobile-nord.com (neu geladen)');

      console.log(`✅ ${allVehicles.length} Fahrzeuge von der Website geladen`);
    } catch (error) {
      console.error('❌ Fehler beim Laden von der Website:', error);
    } finally {
      setScrapingFromWebsite(false);
    }
  };

  const getVehiclesByStatus = (status) => {
    return vehicles.filter(v => v.status === status);
  };

  const VehicleCard = ({ vehicle }) => (
    <div
      draggable
      onDragStart={(e) => handleDragStart(e, vehicle)}
      className="vehicle-card bg-white p-3 rounded-lg shadow-sm border border-gray-200 cursor-move mb-2"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FaCar className="text-blue-500 mr-2" size={16} />
          <div>
            <div className="font-medium text-sm">{vehicle.make}</div>
            <div className="text-xs text-gray-500">{vehicle.model} ({vehicle.year})</div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs font-bold">{vehicle.price?.toLocaleString('de-DE')} €</div>
          <div className="text-xs text-gray-500">{(vehicle.mileage || vehicle.km)?.toLocaleString('de-DE')} km</div>
        </div>
      </div>
      {vehicle.smartcarConnected && (
        <div className="mt-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
          🔋 Smartcar verbunden
        </div>
      )}
    </div>
  );

  const StatusZone = ({ status, statusInfo }) => {
    const vehiclesInZone = getVehiclesByStatus(status);
    const Icon = statusInfo.icon;

    return (
      <div
        className={`drag-zone ${statusInfo.color} border-2 border-dashed rounded-lg p-4 min-h-[300px] hover:border-solid`}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, status)}
      >
        <div className="flex items-center mb-4">
          <Icon className={`${statusInfo.textColor} mr-2`} size={20} />
          <h3 className={`font-semibold ${statusInfo.textColor}`}>
            {statusInfo.name} ({vehiclesInZone.length})
          </h3>
        </div>
        
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {vehiclesInZone.map(vehicle => (
            <VehicleCard key={vehicle.id} vehicle={vehicle} />
          ))}
          
          {vehiclesInZone.length === 0 && (
            <div className={`text-center py-8 ${statusInfo.textColor} opacity-50`}>
              <Icon size={32} className="mx-auto mb-2" />
              <p className="text-sm">Keine Fahrzeuge</p>
              <p className="text-xs">Fahrzeuge hierher ziehen</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="spinner mb-4"></div>
            <h2 className="text-xl font-semibold">Lade Fahrzeugdaten...</h2>
            <p className="text-gray-600">Fahrzeuge werden geladen</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold mb-2">🚗 Fahrzeug-Management</h1>
            <p className="text-gray-600">
              Ziehen Sie Fahrzeuge zwischen den Bereichen, um deren Status zu ändern
            </p>
            <div className="flex items-center gap-2 mt-2">
              <FaGlobe size={14} className="text-blue-500" />
              <span className="text-sm text-gray-600">
                Datenquelle: {dataSource}
              </span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
            <button
              onClick={loadFromWebsite}
              disabled={scrapingFromWebsite}
              className="btn"
              style={{
                backgroundColor: scrapingFromWebsite ? 'var(--border-color)' : 'var(--primary-color)',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '6px',
                cursor: scrapingFromWebsite ? 'not-allowed' : 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              <FaDownload style={{ animation: scrapingFromWebsite ? 'spin 1s linear infinite' : 'none' }} />
              {scrapingFromWebsite ? 'Lade von Website...' : 'Von Website laden'}
            </button>

            <button
              onClick={refreshData}
              disabled={refreshing}
              className="btn"
              style={{
                backgroundColor: refreshing ? 'var(--border-color)' : 'var(--success-color)',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.75rem 1rem',
                border: 'none',
                borderRadius: '6px',
                cursor: refreshing ? 'not-allowed' : 'pointer',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              <FaSync style={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
              {refreshing ? 'Aktualisiere...' : 'Aktualisieren'}
            </button>
          </div>
        </div>
      </div>

      {/* Statistiken */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
        {Object.entries(STATUS_ZONES).map(([status, info]) => {
          const count = getVehiclesByStatus(status).length;
          const Icon = info.icon;
          return (
            <div key={status} className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="flex items-center">
                <Icon className={info.textColor} size={20} />
                <div className="ml-3">
                  <div className="text-2xl font-bold">{count}</div>
                  <div className="text-sm text-gray-500">{info.name}</div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Drag & Drop Bereiche */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(STATUS_ZONES).map(([status, statusInfo]) => (
          <StatusZone key={status} status={status} statusInfo={statusInfo} />
        ))}
      </div>

      {/* Hinweise */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Drag & Drop Funktionalität</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>• Fahrzeuge per Drag & Drop zwischen Bereichen verschieben</p>
                <p>• Änderungen werden automatisch gespeichert</p>
                <p>• Smartcar-verbundene Fahrzeuge sind markiert</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FaGlobe className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Datenquellen</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>• "Von Website laden" - Lädt echte Daten von automobile-nord.com</p>
                <p>• "Aktualisieren" - Erneuert die aktuellen Daten</p>
                <p>• Automatische Fallback-Mechanismen bei Fehlern</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DragDropDashboard;