// Echter Fahrzeug-Service für Daten von automobile-nord.com und mobile.de
import apiService from './apiService';
import mockBackendService from './mockBackendService';

class RealVehicleService {
  constructor() {
    this.vehicles = [];
    this.isLoading = false;
    this.lastUpdate = null;
    this.useBackend = true;
  }

  // Prüfe Backend-Verfügbarkeit
  async checkBackendHealth() {
    try {
      const health = await apiService.checkHealth();
      this.useBackend = health.status === 'ok' || health.success;
      return this.useBackend;
    } catch (error) {
      console.warn('Echtes Backend nicht erreichbar, verwende Mock-Backend');
      // Verwende Mock-Backend als Fallback
      try {
        const mockHealth = await mockBackendService.checkHealth();
        this.useBackend = false; // Markiere als Mock
        return mockHealth.success;
      } catch (mockError) {
        console.error('Auch Mock-Backend fehlgeschlagen');
        this.useBackend = false;
        return false;
      }
    }
  }

  // Lade Fahrzeuge von automobile-nord.com
  async loadVehiclesFromWebsite() {
    try {
      console.log('🚗 Lade Fahrzeuge von automobile-nord.com...');
      this.isLoading = true;

      // Prüfe Backend-Verfügbarkeit
      const backendAvailable = await this.checkBackendHealth();
      
      if (!backendAvailable) {
        console.warn('Echtes Backend nicht verfügbar, verwende Mock-Backend');
        const mockResult = await mockBackendService.scrapeAutomobileNord(true);
        if (mockResult.success) {
          this.vehicles = this.normalizeVehicleData(mockResult.data);
          this.lastUpdate = new Date();
          console.log(`✅ ${this.vehicles.length} Fahrzeuge vom Mock-Backend geladen`);
          return this.vehicles;
        }
        return this.getFallbackVehicles();
      }

      // Scrape Fahrzeuge von der echten Website
      const result = await apiService.scrapeAutomobileNord(true);
      
      if (result.success && result.data) {
        this.vehicles = this.normalizeVehicleData(result.data);
        this.lastUpdate = new Date();
        
        // Speichere in localStorage als Cache
        localStorage.setItem('real_vehicles', JSON.stringify(this.vehicles));
        localStorage.setItem('real_vehicles_timestamp', this.lastUpdate.toISOString());
        
        console.log(`✅ ${this.vehicles.length} Fahrzeuge von automobile-nord.com geladen`);
        return this.vehicles;
      } else {
        throw new Error('Keine Fahrzeugdaten erhalten');
      }
    } catch (error) {
      console.error('❌ Fehler beim Laden von automobile-nord.com:', error);
      
      // Versuche Cache zu laden
      const cachedVehicles = this.loadFromCache();
      if (cachedVehicles.length > 0) {
        console.log('📦 Verwende gecachte Fahrzeugdaten');
        this.vehicles = cachedVehicles;
        return this.vehicles;
      }
      
      // Fallback zu Beispieldaten
      console.log('🔄 Verwende Fallback-Daten');
      return this.getFallbackVehicles();
    } finally {
      this.isLoading = false;
    }
  }

  // Lade zusätzliche Marktdaten von mobile.de
  async enrichWithMarketData(searchQuery = '') {
    try {
      if (!this.useBackend) return;

      console.log('📊 Lade Marktdaten von mobile.de...');
      const marketData = await apiService.scrapeMobileDe(searchQuery);
      
      if (marketData.success && marketData.data) {
        console.log(`✅ ${marketData.data.marketData?.length || 0} Marktdaten von mobile.de geladen`);
        return marketData.data;
      }
    } catch (error) {
      console.error('❌ Fehler beim Laden von mobile.de:', error);
    }
    return null;
  }

  // Normalisiere Fahrzeugdaten für einheitliche Struktur
  normalizeVehicleData(vehicles) {
    return vehicles.map((vehicle, index) => ({
      id: vehicle.id || `real-${index + 1}`,
      make: vehicle.make || vehicle.brand || 'Unbekannt',
      model: vehicle.model || 'Unbekannt',
      year: vehicle.year || new Date().getFullYear(),
      price: vehicle.price || 0,
      status: this.normalizeStatus(vehicle.status),
      type: vehicle.type || vehicle.category || 'PKW',
      fuelType: vehicle.fuelType || vehicle.fuel || 'Benzin',
      mileage: vehicle.mileage || vehicle.km || 0,
      color: vehicle.color || 'Unbekannt',
      vin: vehicle.vin || `VIN${index.toString().padStart(9, '0')}`,
      location: vehicle.location || `Stellplatz ${String.fromCharCode(65 + Math.floor(index/10))}${(index%10)+1}`,
      smartcarConnected: vehicle.smartcarConnected || false,
      battery: vehicle.battery || null,
      image: vehicle.image || null,
      description: vehicle.description || '',
      features: vehicle.features || [],
      marketAnalysis: vehicle.marketAnalysis || null,
      createdAt: vehicle.createdAt || new Date().toISOString(),
      updatedAt: vehicle.updatedAt || new Date().toISOString()
    }));
  }

  // Normalisiere Status-Werte
  normalizeStatus(status) {
    if (!status) return 'verfügbar';
    
    const statusMap = {
      'In Inventory': 'verfügbar',
      'In Transit': 'transport',
      'In Inspection': 'prüfung',
      'In Wash': 'wäsche',
      'In Export': 'export',
      'Maintenance': 'werkstatt',
      'Sold': 'verkauft',
      'verfügbar': 'verfügbar',
      'verkauft': 'verkauft',
      'reserviert': 'reserviert',
      'werkstatt': 'werkstatt'
    };
    
    return statusMap[status] || 'verfügbar';
  }

  // Lade aus Cache
  loadFromCache() {
    try {
      const cached = localStorage.getItem('real_vehicles');
      const timestamp = localStorage.getItem('real_vehicles_timestamp');
      
      if (cached && timestamp) {
        const cacheAge = Date.now() - new Date(timestamp).getTime();
        const maxAge = 30 * 60 * 1000; // 30 Minuten
        
        if (cacheAge < maxAge) {
          return JSON.parse(cached);
        }
      }
    } catch (error) {
      console.error('Fehler beim Laden aus Cache:', error);
    }
    return [];
  }

  // Fallback-Fahrzeuge wenn Backend nicht verfügbar
  getFallbackVehicles() {
    const fallbackData = [
      { id: 'fb-001', make: 'BMW', model: '320i', year: 2023, price: 45000, status: 'verfügbar', type: 'Limousine', fuelType: 'Benzin', mileage: 12500, color: 'Schwarz', vin: 'WBA123456789', location: 'Stellplatz A1' },
      { id: 'fb-002', make: 'Mercedes', model: 'C-Klasse', year: 2022, price: 52000, status: 'verkauft', type: 'Limousine', fuelType: 'Diesel', mileage: 25000, color: 'Silber', vin: 'WDD987654321', location: 'Stellplatz A2' },
      { id: 'fb-003', make: 'Audi', model: 'A4', year: 2023, price: 48000, status: 'reserviert', type: 'Limousine', fuelType: 'Benzin', mileage: 8500, color: 'Weiß', vin: 'WAU456789123', location: 'Stellplatz B1' },
    ];
    
    this.vehicles = fallbackData;
    return fallbackData;
  }

  // Alle Fahrzeuge abrufen
  async getAllVehicles() {
    if (this.vehicles.length === 0 || this.shouldRefresh()) {
      await this.loadVehiclesFromWebsite();
    }
    return this.vehicles;
  }

  // Prüfe ob Daten aktualisiert werden sollten
  shouldRefresh() {
    if (!this.lastUpdate) return true;
    const age = Date.now() - this.lastUpdate.getTime();
    return age > 15 * 60 * 1000; // 15 Minuten
  }

  // Einzelnes Fahrzeug abrufen
  getVehicleById(id) {
    return this.vehicles.find(v => v.id === id);
  }

  // Fahrzeuge nach Status filtern
  getVehiclesByStatus(status) {
    return this.vehicles.filter(v => v.status === status);
  }

  // Fahrzeuge durchsuchen
  searchVehicles(query) {
    return apiService.searchVehicles(this.vehicles, query);
  }

  // Statistiken berechnen
  getStatistics() {
    return apiService.calculateStatistics(this.vehicles);
  }

  // Fahrzeug hinzufügen (über Backend)
  async addVehicle(vehicle) {
    try {
      if (this.useBackend) {
        const result = await apiService.createVehicle(vehicle);
        if (result.success) {
          this.vehicles.push(result.data);
          return result.data;
        }
      }
      
      // Fallback: lokales Hinzufügen
      const newId = `local-${Date.now()}`;
      const newVehicle = { ...vehicle, id: newId };
      this.vehicles.push(newVehicle);
      return newVehicle;
    } catch (error) {
      console.error('Fehler beim Hinzufügen des Fahrzeugs:', error);
      throw error;
    }
  }

  // Fahrzeug aktualisieren
  async updateVehicle(id, updates) {
    try {
      if (this.useBackend) {
        const result = await apiService.updateVehicle(id, updates);
        if (result.success) {
          const index = this.vehicles.findIndex(v => v.id === id);
          if (index !== -1) {
            this.vehicles[index] = { ...this.vehicles[index], ...updates };
          }
          return result.data;
        }
      }
      
      // Fallback: lokale Aktualisierung
      const index = this.vehicles.findIndex(v => v.id === id);
      if (index !== -1) {
        this.vehicles[index] = { ...this.vehicles[index], ...updates };
        return this.vehicles[index];
      }
      return null;
    } catch (error) {
      console.error('Fehler beim Aktualisieren des Fahrzeugs:', error);
      throw error;
    }
  }

  // Fahrzeug löschen
  async deleteVehicle(id) {
    try {
      if (this.useBackend) {
        const result = await apiService.deleteVehicle(id);
        if (result.success) {
          this.vehicles = this.vehicles.filter(v => v.id !== id);
          return true;
        }
      }
      
      // Fallback: lokales Löschen
      this.vehicles = this.vehicles.filter(v => v.id !== id);
      return true;
    } catch (error) {
      console.error('Fehler beim Löschen des Fahrzeugs:', error);
      throw error;
    }
  }

  // Daten manuell aktualisieren
  async refreshData() {
    this.lastUpdate = null;
    return await this.loadVehiclesFromWebsite();
  }
}

const realVehicleService = new RealVehicleService();
export default realVehicleService;
