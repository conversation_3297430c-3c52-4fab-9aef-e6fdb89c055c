{"ast": null, "code": "// Mock Backend Service - Simuliert echte Daten von automobile-nord.com und mobile.de\n// Wird verwendet bis das echte Backend konfiguriert ist\n\nclass MockBackendService {\n  constructor() {\n    this.isOnline = true;\n    this.lastScrapeTime = null;\n  }\n\n  // Simuliere Gesundheitsstatus\n  async checkHealth() {\n    await this.delay(100);\n    return {\n      success: true,\n      status: 'ok',\n      message: 'Mock Backend läuft',\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // Simuliere Scraping von automobile-nord.com\n  async scrapeAutomobileNord(importData = false) {\n    console.log('🔄 Simuliere Scraping von automobile-nord.com...');\n    await this.delay(2000); // Simuliere Netzwerkverzögerung\n\n    const mockVehicles = this.generateRealisticVehicles();\n    this.lastScrapeTime = new Date();\n    return {\n      success: true,\n      data: mockVehicles,\n      vehiclesFound: mockVehicles.length,\n      source: 'automobile-nord.com (simuliert)',\n      timestamp: this.lastScrapeTime.toISOString()\n    };\n  }\n\n  // Simuliere mobile.de Marktdaten\n  async scrapeMobileDe(searchQuery = '') {\n    console.log('📊 Simuliere mobile.de Marktdaten...');\n    await this.delay(1500);\n    const marketData = this.generateMarketData(searchQuery);\n    return {\n      success: true,\n      data: {\n        marketData,\n        count: marketData.length,\n        searchQuery,\n        source: 'mobile.de (simuliert)'\n      }\n    };\n  }\n\n  // Generiere realistische Fahrzeugdaten\n  generateRealisticVehicles() {\n    const vehicles = [];\n    const makes = ['BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Porsche', 'Tesla', 'Ford', 'Opel'];\n    const models = {\n      'BMW': ['320i', '520d', 'X3', 'X5', 'i4', '330e'],\n      'Mercedes-Benz': ['C-Klasse', 'E-Klasse', 'GLC', 'GLE', 'A-Klasse', 'CLA'],\n      'Audi': ['A4', 'A6', 'Q5', 'Q7', 'e-tron', 'A3'],\n      'Volkswagen': ['Golf', 'Passat', 'Tiguan', 'Touareg', 'ID.4', 'Arteon'],\n      'Porsche': ['911', 'Cayenne', 'Macan', 'Panamera', 'Taycan'],\n      'Tesla': ['Model 3', 'Model S', 'Model X', 'Model Y'],\n      'Ford': ['Focus', 'Kuga', 'Mustang', 'Explorer', 'Mondeo'],\n      'Opel': ['Astra', 'Insignia', 'Mokka', 'Grandland', 'Corsa']\n    };\n    const colors = ['Schwarz', 'Weiß', 'Silber', 'Grau', 'Blau', 'Rot', 'Braun'];\n    const fuelTypes = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];\n    const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];\n    for (let i = 1; i <= 25; i++) {\n      const make = makes[Math.floor(Math.random() * makes.length)];\n      const modelOptions = models[make];\n      const model = modelOptions[Math.floor(Math.random() * modelOptions.length)];\n      const year = 2020 + Math.floor(Math.random() * 4);\n      const isElectric = model.includes('e-tron') || model.includes('ID.') || model.includes('Taycan') || make === 'Tesla';\n      const fuelType = isElectric ? 'Elektro' : fuelTypes[Math.floor(Math.random() * fuelTypes.length)];\n\n      // Realistische Preise basierend auf Marke und Jahr\n      let basePrice = 25000;\n      if (make === 'Porsche') basePrice = 80000;else if (make === 'BMW' || make === 'Mercedes-Benz') basePrice = 45000;else if (make === 'Audi') basePrice = 40000;else if (make === 'Tesla') basePrice = 50000;\n      const price = basePrice + Math.floor(Math.random() * 30000) + (year - 2020) * 5000;\n      const mileage = Math.floor(Math.random() * 80000) + 5000;\n      vehicles.push({\n        id: `an-${i.toString().padStart(3, '0')}`,\n        make,\n        model,\n        year,\n        price,\n        status: statuses[Math.floor(Math.random() * statuses.length)],\n        type: this.getVehicleType(model),\n        fuelType,\n        mileage,\n        color: colors[Math.floor(Math.random() * colors.length)],\n        vin: `${make.substring(0, 3).toUpperCase()}${i.toString().padStart(9, '0')}`,\n        location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i / 10))}${i % 10 + 1}`,\n        smartcarConnected: isElectric && Math.random() < 0.7,\n        // 70% der E-Autos haben Smartcar\n        battery: isElectric ? Math.floor(Math.random() * 40) + 60 : null,\n        description: `Gepflegter ${make} ${model} in ${colors[Math.floor(Math.random() * colors.length)]}. Aus erster Hand.`,\n        features: this.generateFeatures(make, model),\n        image: null,\n        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n        updatedAt: new Date().toISOString(),\n        source: 'automobile-nord.com'\n      });\n    }\n    return vehicles;\n  }\n\n  // Bestimme Fahrzeugtyp basierend auf Modell\n  getVehicleType(model) {\n    if (model.includes('X') || model.includes('Q') || model.includes('GLC') || model.includes('GLE') || model.includes('Tiguan') || model.includes('Cayenne') || model.includes('Macan') || model.includes('Kuga') || model.includes('Mokka') || model.includes('Grandland')) {\n      return 'SUV';\n    }\n    if (model.includes('Kombi') || model.includes('Touring') || model.includes('Avant')) {\n      return 'Kombi';\n    }\n    if (model.includes('Cabrio') || model.includes('Roadster')) {\n      return 'Cabrio';\n    }\n    if (model.includes('Coupe') || model.includes('911')) {\n      return 'Coupe';\n    }\n    return 'Limousine';\n  }\n\n  // Generiere Fahrzeugausstattung\n  generateFeatures(make, model) {\n    const baseFeatures = ['ABS', 'ESP', 'Klimaanlage', 'Zentralverriegelung'];\n    const premiumFeatures = ['Navigationssystem', 'Ledersitze', 'Sitzheizung', 'Xenon-Licht', 'Tempomat'];\n    const luxuryFeatures = ['Massagesitze', 'Head-Up Display', 'Surround-Sound', 'Luftfederung'];\n    let features = [...baseFeatures];\n    if (make === 'BMW' || make === 'Mercedes-Benz' || make === 'Audi') {\n      features.push(...premiumFeatures.slice(0, 3));\n    }\n    if (make === 'Porsche' || model.includes('S-Klasse') || model.includes('7er')) {\n      features.push(...luxuryFeatures.slice(0, 2));\n    }\n    return features;\n  }\n\n  // Generiere Marktdaten\n  generateMarketData(searchQuery) {\n    const marketData = [];\n    for (let i = 0; i < 15; i++) {\n      marketData.push({\n        title: `${searchQuery || 'BMW 320i'} - Marktvergleich ${i + 1}`,\n        price: 35000 + Math.floor(Math.random() * 20000),\n        mileage: Math.floor(Math.random() * 100000) + 10000,\n        year: 2020 + Math.floor(Math.random() * 4),\n        source: 'mobile.de'\n      });\n    }\n    return marketData;\n  }\n\n  // Simuliere Netzwerkverzögerung\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  // Alle Fahrzeuge abrufen (simuliert Backend-API)\n  async getAllVehicles(params = {}) {\n    await this.delay(500);\n    const vehicles = this.generateRealisticVehicles();\n    return {\n      success: true,\n      data: vehicles,\n      pagination: {\n        page: 1,\n        limit: 50,\n        total: vehicles.length,\n        totalPages: 1\n      }\n    };\n  }\n\n  // Einzelnes Fahrzeug abrufen\n  async getVehicleById(id) {\n    await this.delay(200);\n    const vehicles = this.generateRealisticVehicles();\n    const vehicle = vehicles.find(v => v.id === id);\n    if (vehicle) {\n      return {\n        success: true,\n        data: vehicle\n      };\n    } else {\n      return {\n        success: false,\n        error: 'Fahrzeug nicht gefunden'\n      };\n    }\n  }\n\n  // Fahrzeug erstellen\n  async createVehicle(vehicleData) {\n    await this.delay(300);\n    const newVehicle = {\n      ...vehicleData,\n      id: `new-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    return {\n      success: true,\n      data: newVehicle\n    };\n  }\n\n  // Fahrzeug aktualisieren\n  async updateVehicle(id, updates) {\n    await this.delay(300);\n    return {\n      success: true,\n      data: {\n        id,\n        ...updates,\n        updatedAt: new Date().toISOString()\n      }\n    };\n  }\n\n  // Fahrzeug löschen\n  async deleteVehicle(id) {\n    await this.delay(200);\n    return {\n      success: true,\n      message: 'Fahrzeug erfolgreich gelöscht'\n    };\n  }\n\n  // Marktpreisanalyse\n  async getMarketAnalysis(make, model, year) {\n    await this.delay(1000);\n    const basePrice = 35000;\n    const variance = 10000;\n    const prices = Array.from({\n      length: 10\n    }, () => basePrice + Math.floor(Math.random() * variance * 2) - variance);\n    const averagePrice = prices.reduce((a, b) => a + b, 0) / prices.length;\n    return {\n      success: true,\n      data: {\n        averagePrice: Math.round(averagePrice),\n        priceRange: {\n          min: Math.min(...prices),\n          max: Math.max(...prices)\n        },\n        sampleSize: prices.length,\n        recommendations: ['Preis liegt im Marktdurchschnitt', 'Gute Verkaufschancen bei aktueller Preisgestaltung', 'Markttrend: stabil']\n      }\n    };\n  }\n}\nconst mockBackendService = new MockBackendService();\nexport default mockBackendService;", "map": {"version": 3, "names": ["MockBackendService", "constructor", "isOnline", "lastScrapeTime", "checkHealth", "delay", "success", "status", "message", "timestamp", "Date", "toISOString", "scrapeAutomobileNord", "importData", "console", "log", "mockVehicles", "generateRealisticVehicles", "data", "vehiclesFound", "length", "source", "scrapeMobileDe", "searchQuery", "marketData", "generateMarketData", "count", "vehicles", "makes", "models", "colors", "fuelTypes", "statuses", "i", "make", "Math", "floor", "random", "modelOptions", "model", "year", "isElectric", "includes", "fuelType", "basePrice", "price", "mileage", "push", "id", "toString", "padStart", "type", "getVehicleType", "color", "vin", "substring", "toUpperCase", "location", "String", "fromCharCode", "smartcarConnected", "battery", "description", "features", "generateFeatures", "image", "createdAt", "now", "updatedAt", "baseFeatures", "premiumFeatures", "luxuryFeatures", "slice", "title", "ms", "Promise", "resolve", "setTimeout", "getAllVehicles", "params", "pagination", "page", "limit", "total", "totalPages", "getVehicleById", "vehicle", "find", "v", "error", "createVehicle", "vehicleData", "newVehicle", "updateVehicle", "updates", "deleteVehicle", "getMarketAnalysis", "variance", "prices", "Array", "from", "averagePrice", "reduce", "a", "b", "round", "priceRange", "min", "max", "sampleSize", "recommendations", "mockBackendService"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/services/mockBackendService.js"], "sourcesContent": ["// Mock Backend Service - Simuliert echte Daten von automobile-nord.com und mobile.de\n// Wird verwendet bis das echte Backend konfiguriert ist\n\nclass MockBackendService {\n  constructor() {\n    this.isOnline = true;\n    this.lastScrapeTime = null;\n  }\n\n  // Simuliere Gesundheitsstatus\n  async checkHealth() {\n    await this.delay(100);\n    return {\n      success: true,\n      status: 'ok',\n      message: 'Mock Backend läuft',\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // Simuliere Scraping von automobile-nord.com\n  async scrapeAutomobileNord(importData = false) {\n    console.log('🔄 Simuliere Scraping von automobile-nord.com...');\n    await this.delay(2000); // Simuliere Netzwerkverzögerung\n\n    const mockVehicles = this.generateRealisticVehicles();\n    this.lastScrapeTime = new Date();\n\n    return {\n      success: true,\n      data: mockVehicles,\n      vehiclesFound: mockVehicles.length,\n      source: 'automobile-nord.com (simuliert)',\n      timestamp: this.lastScrapeTime.toISOString()\n    };\n  }\n\n  // Simuliere mobile.de Marktdaten\n  async scrapeMobileDe(searchQuery = '') {\n    console.log('📊 Simuliere mobile.de Marktdaten...');\n    await this.delay(1500);\n\n    const marketData = this.generateMarketData(searchQuery);\n\n    return {\n      success: true,\n      data: {\n        marketData,\n        count: marketData.length,\n        searchQuery,\n        source: 'mobile.de (simuliert)'\n      }\n    };\n  }\n\n  // Generiere realistische Fahrzeugdaten\n  generateRealisticVehicles() {\n    const vehicles = [];\n    const makes = ['BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Porsche', 'Tesla', 'Ford', 'Opel'];\n    const models = {\n      'BMW': ['320i', '520d', 'X3', 'X5', 'i4', '330e'],\n      'Mercedes-Benz': ['C-Klasse', 'E-Klasse', 'GLC', 'GLE', 'A-Klasse', 'CLA'],\n      'Audi': ['A4', 'A6', 'Q5', 'Q7', 'e-tron', 'A3'],\n      'Volkswagen': ['Golf', 'Passat', 'Tiguan', 'Touareg', 'ID.4', 'Arteon'],\n      'Porsche': ['911', 'Cayenne', 'Macan', 'Panamera', 'Taycan'],\n      'Tesla': ['Model 3', 'Model S', 'Model X', 'Model Y'],\n      'Ford': ['Focus', 'Kuga', 'Mustang', 'Explorer', 'Mondeo'],\n      'Opel': ['Astra', 'Insignia', 'Mokka', 'Grandland', 'Corsa']\n    };\n    \n    const colors = ['Schwarz', 'Weiß', 'Silber', 'Grau', 'Blau', 'Rot', 'Braun'];\n    const fuelTypes = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];\n    const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];\n\n    for (let i = 1; i <= 25; i++) {\n      const make = makes[Math.floor(Math.random() * makes.length)];\n      const modelOptions = models[make];\n      const model = modelOptions[Math.floor(Math.random() * modelOptions.length)];\n      const year = 2020 + Math.floor(Math.random() * 4);\n      const isElectric = model.includes('e-tron') || model.includes('ID.') || model.includes('Taycan') || make === 'Tesla';\n      const fuelType = isElectric ? 'Elektro' : fuelTypes[Math.floor(Math.random() * fuelTypes.length)];\n      \n      // Realistische Preise basierend auf Marke und Jahr\n      let basePrice = 25000;\n      if (make === 'Porsche') basePrice = 80000;\n      else if (make === 'BMW' || make === 'Mercedes-Benz') basePrice = 45000;\n      else if (make === 'Audi') basePrice = 40000;\n      else if (make === 'Tesla') basePrice = 50000;\n      \n      const price = basePrice + Math.floor(Math.random() * 30000) + (year - 2020) * 5000;\n      const mileage = Math.floor(Math.random() * 80000) + 5000;\n      \n      vehicles.push({\n        id: `an-${i.toString().padStart(3, '0')}`,\n        make,\n        model,\n        year,\n        price,\n        status: statuses[Math.floor(Math.random() * statuses.length)],\n        type: this.getVehicleType(model),\n        fuelType,\n        mileage,\n        color: colors[Math.floor(Math.random() * colors.length)],\n        vin: `${make.substring(0, 3).toUpperCase()}${i.toString().padStart(9, '0')}`,\n        location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i/10))}${(i%10)+1}`,\n        smartcarConnected: isElectric && Math.random() < 0.7, // 70% der E-Autos haben Smartcar\n        battery: isElectric ? Math.floor(Math.random() * 40) + 60 : null,\n        description: `Gepflegter ${make} ${model} in ${colors[Math.floor(Math.random() * colors.length)]}. Aus erster Hand.`,\n        features: this.generateFeatures(make, model),\n        image: null,\n        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),\n        updatedAt: new Date().toISOString(),\n        source: 'automobile-nord.com'\n      });\n    }\n\n    return vehicles;\n  }\n\n  // Bestimme Fahrzeugtyp basierend auf Modell\n  getVehicleType(model) {\n    if (model.includes('X') || model.includes('Q') || model.includes('GLC') || model.includes('GLE') || \n        model.includes('Tiguan') || model.includes('Cayenne') || model.includes('Macan') || \n        model.includes('Kuga') || model.includes('Mokka') || model.includes('Grandland')) {\n      return 'SUV';\n    }\n    if (model.includes('Kombi') || model.includes('Touring') || model.includes('Avant')) {\n      return 'Kombi';\n    }\n    if (model.includes('Cabrio') || model.includes('Roadster')) {\n      return 'Cabrio';\n    }\n    if (model.includes('Coupe') || model.includes('911')) {\n      return 'Coupe';\n    }\n    return 'Limousine';\n  }\n\n  // Generiere Fahrzeugausstattung\n  generateFeatures(make, model) {\n    const baseFeatures = ['ABS', 'ESP', 'Klimaanlage', 'Zentralverriegelung'];\n    const premiumFeatures = ['Navigationssystem', 'Ledersitze', 'Sitzheizung', 'Xenon-Licht', 'Tempomat'];\n    const luxuryFeatures = ['Massagesitze', 'Head-Up Display', 'Surround-Sound', 'Luftfederung'];\n    \n    let features = [...baseFeatures];\n    \n    if (make === 'BMW' || make === 'Mercedes-Benz' || make === 'Audi') {\n      features.push(...premiumFeatures.slice(0, 3));\n    }\n    \n    if (make === 'Porsche' || model.includes('S-Klasse') || model.includes('7er')) {\n      features.push(...luxuryFeatures.slice(0, 2));\n    }\n    \n    return features;\n  }\n\n  // Generiere Marktdaten\n  generateMarketData(searchQuery) {\n    const marketData = [];\n    \n    for (let i = 0; i < 15; i++) {\n      marketData.push({\n        title: `${searchQuery || 'BMW 320i'} - Marktvergleich ${i + 1}`,\n        price: 35000 + Math.floor(Math.random() * 20000),\n        mileage: Math.floor(Math.random() * 100000) + 10000,\n        year: 2020 + Math.floor(Math.random() * 4),\n        source: 'mobile.de'\n      });\n    }\n    \n    return marketData;\n  }\n\n  // Simuliere Netzwerkverzögerung\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  // Alle Fahrzeuge abrufen (simuliert Backend-API)\n  async getAllVehicles(params = {}) {\n    await this.delay(500);\n    \n    const vehicles = this.generateRealisticVehicles();\n    \n    return {\n      success: true,\n      data: vehicles,\n      pagination: {\n        page: 1,\n        limit: 50,\n        total: vehicles.length,\n        totalPages: 1\n      }\n    };\n  }\n\n  // Einzelnes Fahrzeug abrufen\n  async getVehicleById(id) {\n    await this.delay(200);\n    \n    const vehicles = this.generateRealisticVehicles();\n    const vehicle = vehicles.find(v => v.id === id);\n    \n    if (vehicle) {\n      return {\n        success: true,\n        data: vehicle\n      };\n    } else {\n      return {\n        success: false,\n        error: 'Fahrzeug nicht gefunden'\n      };\n    }\n  }\n\n  // Fahrzeug erstellen\n  async createVehicle(vehicleData) {\n    await this.delay(300);\n    \n    const newVehicle = {\n      ...vehicleData,\n      id: `new-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n    \n    return {\n      success: true,\n      data: newVehicle\n    };\n  }\n\n  // Fahrzeug aktualisieren\n  async updateVehicle(id, updates) {\n    await this.delay(300);\n    \n    return {\n      success: true,\n      data: {\n        id,\n        ...updates,\n        updatedAt: new Date().toISOString()\n      }\n    };\n  }\n\n  // Fahrzeug löschen\n  async deleteVehicle(id) {\n    await this.delay(200);\n    \n    return {\n      success: true,\n      message: 'Fahrzeug erfolgreich gelöscht'\n    };\n  }\n\n  // Marktpreisanalyse\n  async getMarketAnalysis(make, model, year) {\n    await this.delay(1000);\n    \n    const basePrice = 35000;\n    const variance = 10000;\n    const prices = Array.from({length: 10}, () => \n      basePrice + Math.floor(Math.random() * variance * 2) - variance\n    );\n    \n    const averagePrice = prices.reduce((a, b) => a + b, 0) / prices.length;\n    \n    return {\n      success: true,\n      data: {\n        averagePrice: Math.round(averagePrice),\n        priceRange: {\n          min: Math.min(...prices),\n          max: Math.max(...prices)\n        },\n        sampleSize: prices.length,\n        recommendations: [\n          'Preis liegt im Marktdurchschnitt',\n          'Gute Verkaufschancen bei aktueller Preisgestaltung',\n          'Markttrend: stabil'\n        ]\n      }\n    };\n  }\n}\n\nconst mockBackendService = new MockBackendService();\nexport default mockBackendService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAG;IAClB,MAAM,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;IACrB,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,oBAAoB;MAC7BC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;EACH;;EAEA;EACA,MAAMC,oBAAoBA,CAACC,UAAU,GAAG,KAAK,EAAE;IAC7CC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAC/D,MAAM,IAAI,CAACV,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;;IAExB,MAAMW,YAAY,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACrD,IAAI,CAACd,cAAc,GAAG,IAAIO,IAAI,CAAC,CAAC;IAEhC,OAAO;MACLJ,OAAO,EAAE,IAAI;MACbY,IAAI,EAAEF,YAAY;MAClBG,aAAa,EAAEH,YAAY,CAACI,MAAM;MAClCC,MAAM,EAAE,iCAAiC;MACzCZ,SAAS,EAAE,IAAI,CAACN,cAAc,CAACQ,WAAW,CAAC;IAC7C,CAAC;EACH;;EAEA;EACA,MAAMW,cAAcA,CAACC,WAAW,GAAG,EAAE,EAAE;IACrCT,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,MAAM,IAAI,CAACV,KAAK,CAAC,IAAI,CAAC;IAEtB,MAAMmB,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAACF,WAAW,CAAC;IAEvD,OAAO;MACLjB,OAAO,EAAE,IAAI;MACbY,IAAI,EAAE;QACJM,UAAU;QACVE,KAAK,EAAEF,UAAU,CAACJ,MAAM;QACxBG,WAAW;QACXF,MAAM,EAAE;MACV;IACF,CAAC;EACH;;EAEA;EACAJ,yBAAyBA,CAAA,EAAG;IAC1B,MAAMU,QAAQ,GAAG,EAAE;IACnB,MAAMC,KAAK,GAAG,CAAC,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IAChG,MAAMC,MAAM,GAAG;MACb,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MACjD,eAAe,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC;MAC1E,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC;MAChD,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;MACvE,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;MAC5D,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACrD,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;MAC1D,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;IAC7D,CAAC;IAED,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;IAC5E,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC3D,MAAMC,QAAQ,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC;IAErE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAMC,IAAI,GAAGN,KAAK,CAACO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGT,KAAK,CAACR,MAAM,CAAC,CAAC;MAC5D,MAAMkB,YAAY,GAAGT,MAAM,CAACK,IAAI,CAAC;MACjC,MAAMK,KAAK,GAAGD,YAAY,CAACH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGC,YAAY,CAAClB,MAAM,CAAC,CAAC;MAC3E,MAAMoB,IAAI,GAAG,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MACjD,MAAMI,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIR,IAAI,KAAK,OAAO;MACpH,MAAMS,QAAQ,GAAGF,UAAU,GAAG,SAAS,GAAGV,SAAS,CAACI,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,SAAS,CAACX,MAAM,CAAC,CAAC;;MAEjG;MACA,IAAIwB,SAAS,GAAG,KAAK;MACrB,IAAIV,IAAI,KAAK,SAAS,EAAEU,SAAS,GAAG,KAAK,CAAC,KACrC,IAAIV,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,eAAe,EAAEU,SAAS,GAAG,KAAK,CAAC,KAClE,IAAIV,IAAI,KAAK,MAAM,EAAEU,SAAS,GAAG,KAAK,CAAC,KACvC,IAAIV,IAAI,KAAK,OAAO,EAAEU,SAAS,GAAG,KAAK;MAE5C,MAAMC,KAAK,GAAGD,SAAS,GAAGT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAACG,IAAI,GAAG,IAAI,IAAI,IAAI;MAClF,MAAMM,OAAO,GAAGX,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI;MAExDV,QAAQ,CAACoB,IAAI,CAAC;QACZC,EAAE,EAAE,MAAMf,CAAC,CAACgB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACzChB,IAAI;QACJK,KAAK;QACLC,IAAI;QACJK,KAAK;QACLtC,MAAM,EAAEyB,QAAQ,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGL,QAAQ,CAACZ,MAAM,CAAC,CAAC;QAC7D+B,IAAI,EAAE,IAAI,CAACC,cAAc,CAACb,KAAK,CAAC;QAChCI,QAAQ;QACRG,OAAO;QACPO,KAAK,EAAEvB,MAAM,CAACK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGP,MAAM,CAACV,MAAM,CAAC,CAAC;QACxDkC,GAAG,EAAE,GAAGpB,IAAI,CAACqB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvB,CAAC,CAACgB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC5EO,QAAQ,EAAE,cAAcC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGxB,IAAI,CAACC,KAAK,CAACH,CAAC,GAAC,EAAE,CAAC,CAAC,GAAIA,CAAC,GAAC,EAAE,GAAE,CAAC,EAAE;QAC/E2B,iBAAiB,EAAEnB,UAAU,IAAIN,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG;QAAE;QACtDwB,OAAO,EAAEpB,UAAU,GAAGN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;QAChEyB,WAAW,EAAE,cAAc5B,IAAI,IAAIK,KAAK,OAAOT,MAAM,CAACK,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGP,MAAM,CAACV,MAAM,CAAC,CAAC,oBAAoB;QACpH2C,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAC9B,IAAI,EAAEK,KAAK,CAAC;QAC5C0B,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAIxD,IAAI,CAACA,IAAI,CAACyD,GAAG,CAAC,CAAC,GAAGhC,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC1B,WAAW,CAAC,CAAC;QACxFyD,SAAS,EAAE,IAAI1D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCU,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA,OAAOM,QAAQ;EACjB;;EAEA;EACAyB,cAAcA,CAACb,KAAK,EAAE;IACpB,IAAIA,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC,IAC5FH,KAAK,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,OAAO,CAAC,IAChFH,KAAK,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,WAAW,CAAC,EAAE;MACpF,OAAO,KAAK;IACd;IACA,IAAIH,KAAK,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,OAAO,CAAC,EAAE;MACnF,OAAO,OAAO;IAChB;IACA,IAAIH,KAAK,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC1D,OAAO,QAAQ;IACjB;IACA,IAAIH,KAAK,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;MACpD,OAAO,OAAO;IAChB;IACA,OAAO,WAAW;EACpB;;EAEA;EACAsB,gBAAgBA,CAAC9B,IAAI,EAAEK,KAAK,EAAE;IAC5B,MAAM8B,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,qBAAqB,CAAC;IACzE,MAAMC,eAAe,GAAG,CAAC,mBAAmB,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,CAAC;IACrG,MAAMC,cAAc,GAAG,CAAC,cAAc,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,CAAC;IAE5F,IAAIR,QAAQ,GAAG,CAAC,GAAGM,YAAY,CAAC;IAEhC,IAAInC,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,MAAM,EAAE;MACjE6B,QAAQ,CAAChB,IAAI,CAAC,GAAGuB,eAAe,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C;IAEA,IAAItC,IAAI,KAAK,SAAS,IAAIK,KAAK,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC7EqB,QAAQ,CAAChB,IAAI,CAAC,GAAGwB,cAAc,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IAEA,OAAOT,QAAQ;EACjB;;EAEA;EACAtC,kBAAkBA,CAACF,WAAW,EAAE;IAC9B,MAAMC,UAAU,GAAG,EAAE;IAErB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BT,UAAU,CAACuB,IAAI,CAAC;QACd0B,KAAK,EAAE,GAAGlD,WAAW,IAAI,UAAU,qBAAqBU,CAAC,GAAG,CAAC,EAAE;QAC/DY,KAAK,EAAE,KAAK,GAAGV,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC;QAChDS,OAAO,EAAEX,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK;QACnDG,IAAI,EAAE,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1ChB,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA,OAAOG,UAAU;EACnB;;EAEA;EACAnB,KAAKA,CAACqE,EAAE,EAAE;IACR,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;EACxD;;EAEA;EACA,MAAMI,cAAcA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,MAAM,IAAI,CAAC1E,KAAK,CAAC,GAAG,CAAC;IAErB,MAAMsB,QAAQ,GAAG,IAAI,CAACV,yBAAyB,CAAC,CAAC;IAEjD,OAAO;MACLX,OAAO,EAAE,IAAI;MACbY,IAAI,EAAES,QAAQ;MACdqD,UAAU,EAAE;QACVC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAExD,QAAQ,CAACP,MAAM;QACtBgE,UAAU,EAAE;MACd;IACF,CAAC;EACH;;EAEA;EACA,MAAMC,cAAcA,CAACrC,EAAE,EAAE;IACvB,MAAM,IAAI,CAAC3C,KAAK,CAAC,GAAG,CAAC;IAErB,MAAMsB,QAAQ,GAAG,IAAI,CAACV,yBAAyB,CAAC,CAAC;IACjD,MAAMqE,OAAO,GAAG3D,QAAQ,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxC,EAAE,KAAKA,EAAE,CAAC;IAE/C,IAAIsC,OAAO,EAAE;MACX,OAAO;QACLhF,OAAO,EAAE,IAAI;QACbY,IAAI,EAAEoE;MACR,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLhF,OAAO,EAAE,KAAK;QACdmF,KAAK,EAAE;MACT,CAAC;IACH;EACF;;EAEA;EACA,MAAMC,aAAaA,CAACC,WAAW,EAAE;IAC/B,MAAM,IAAI,CAACtF,KAAK,CAAC,GAAG,CAAC;IAErB,MAAMuF,UAAU,GAAG;MACjB,GAAGD,WAAW;MACd3C,EAAE,EAAE,OAAOtC,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE;MACvBD,SAAS,EAAE,IAAIxD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCyD,SAAS,EAAE,IAAI1D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,OAAO;MACLL,OAAO,EAAE,IAAI;MACbY,IAAI,EAAE0E;IACR,CAAC;EACH;;EAEA;EACA,MAAMC,aAAaA,CAAC7C,EAAE,EAAE8C,OAAO,EAAE;IAC/B,MAAM,IAAI,CAACzF,KAAK,CAAC,GAAG,CAAC;IAErB,OAAO;MACLC,OAAO,EAAE,IAAI;MACbY,IAAI,EAAE;QACJ8B,EAAE;QACF,GAAG8C,OAAO;QACV1B,SAAS,EAAE,IAAI1D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC;IACF,CAAC;EACH;;EAEA;EACA,MAAMoF,aAAaA,CAAC/C,EAAE,EAAE;IACtB,MAAM,IAAI,CAAC3C,KAAK,CAAC,GAAG,CAAC;IAErB,OAAO;MACLC,OAAO,EAAE,IAAI;MACbE,OAAO,EAAE;IACX,CAAC;EACH;;EAEA;EACA,MAAMwF,iBAAiBA,CAAC9D,IAAI,EAAEK,KAAK,EAAEC,IAAI,EAAE;IACzC,MAAM,IAAI,CAACnC,KAAK,CAAC,IAAI,CAAC;IAEtB,MAAMuC,SAAS,GAAG,KAAK;IACvB,MAAMqD,QAAQ,GAAG,KAAK;IACtB,MAAMC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAChF,MAAM,EAAE;IAAE,CAAC,EAAE,MACtCwB,SAAS,GAAGT,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG4D,QAAQ,GAAG,CAAC,CAAC,GAAGA,QACzD,CAAC;IAED,MAAMI,YAAY,GAAGH,MAAM,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGN,MAAM,CAAC9E,MAAM;IAEtE,OAAO;MACLd,OAAO,EAAE,IAAI;MACbY,IAAI,EAAE;QACJmF,YAAY,EAAElE,IAAI,CAACsE,KAAK,CAACJ,YAAY,CAAC;QACtCK,UAAU,EAAE;UACVC,GAAG,EAAExE,IAAI,CAACwE,GAAG,CAAC,GAAGT,MAAM,CAAC;UACxBU,GAAG,EAAEzE,IAAI,CAACyE,GAAG,CAAC,GAAGV,MAAM;QACzB,CAAC;QACDW,UAAU,EAAEX,MAAM,CAAC9E,MAAM;QACzB0F,eAAe,EAAE,CACf,kCAAkC,EAClC,oDAAoD,EACpD,oBAAoB;MAExB;IACF,CAAC;EACH;AACF;AAEA,MAAMC,kBAAkB,GAAG,IAAI/G,kBAAkB,CAAC,CAAC;AACnD,eAAe+G,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}