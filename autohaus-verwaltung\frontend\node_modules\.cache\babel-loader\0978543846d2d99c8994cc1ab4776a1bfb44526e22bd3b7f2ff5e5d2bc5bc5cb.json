{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\vceeeeeettttttt\\\\autohaus-verwaltung\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaCar, FaBatteryHalf, FaPlus, FaSearch, FaChartBar, FaSync, FaGlobe } from 'react-icons/fa';\nimport realVehicleService from '../services/realVehicleService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Dashboard() {\n  _s();\n  const [vehicles, setVehicles] = useState([]);\n  const [filteredVehicles, setFilteredVehicles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState('alle');\n  const [statistics, setStatistics] = useState({});\n  const [showSmartcarOnly, setShowSmartcarOnly] = useState(false);\n  const [dataSource, setDataSource] = useState('loading...');\n  useEffect(() => {\n    loadVehicles();\n  }, []);\n  const loadVehicles = async () => {\n    try {\n      setLoading(true);\n      console.log('🚗 Lade echte Fahrzeugdaten...');\n\n      // Lade Fahrzeuge von automobile-nord.com\n      const allVehicles = await realVehicleService.getAllVehicles();\n      const stats = realVehicleService.getStatistics();\n      setVehicles(allVehicles);\n      setStatistics(stats);\n\n      // Bestimme Datenquelle\n      if (allVehicles.length > 0) {\n        var _allVehicles$0$id, _allVehicles$0$id2, _allVehicles$0$id3;\n        if ((_allVehicles$0$id = allVehicles[0].id) !== null && _allVehicles$0$id !== void 0 && _allVehicles$0$id.startsWith('fb-')) {\n          setDataSource('Fallback-Daten (Backend nicht erreichbar)');\n        } else if ((_allVehicles$0$id2 = allVehicles[0].id) !== null && _allVehicles$0$id2 !== void 0 && _allVehicles$0$id2.startsWith('an-')) {\n          setDataSource('automobile-nord.com (simuliert)');\n        } else if ((_allVehicles$0$id3 = allVehicles[0].id) !== null && _allVehicles$0$id3 !== void 0 && _allVehicles$0$id3.startsWith('real-')) {\n          setDataSource('automobile-nord.com (echt)');\n        } else {\n          setDataSource('Backend API');\n        }\n      } else {\n        setDataSource('Keine Daten verfügbar');\n      }\n      console.log(`✅ ${allVehicles.length} Fahrzeuge geladen von: ${dataSource}`);\n    } catch (error) {\n      console.error('❌ Fehler beim Laden der Fahrzeuge:', error);\n      setDataSource('Fehler beim Laden');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filterVehicles = useCallback(() => {\n    let filtered = vehicles;\n\n    // Suchfilter\n    if (searchQuery) {\n      filtered = realVehicleService.searchVehicles(filtered, searchQuery);\n    }\n\n    // Status-Filter\n    if (statusFilter !== 'alle') {\n      filtered = filtered.filter(v => v.status === statusFilter);\n    }\n\n    // Smartcar-Filter\n    if (showSmartcarOnly) {\n      filtered = filtered.filter(v => v.smartcarConnected);\n    }\n    setFilteredVehicles(filtered);\n  }, [vehicles, searchQuery, statusFilter, showSmartcarOnly]);\n  useEffect(() => {\n    filterVehicles();\n  }, [filterVehicles]);\n\n  // Daten manuell aktualisieren\n  const refreshData = async () => {\n    try {\n      setRefreshing(true);\n      console.log('🔄 Aktualisiere Fahrzeugdaten...');\n      const allVehicles = await realVehicleService.refreshData();\n      const stats = realVehicleService.getStatistics();\n      setVehicles(allVehicles);\n      setStatistics(stats);\n      console.log(`✅ ${allVehicles.length} Fahrzeuge aktualisiert`);\n    } catch (error) {\n      console.error('❌ Fehler beim Aktualisieren:', error);\n    } finally {\n      setRefreshing(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'verfügbar':\n        return 'var(--success-color)';\n      case 'verkauft':\n        return 'var(--secondary-color)';\n      case 'reserviert':\n        return 'var(--warning-color)';\n      case 'werkstatt':\n        return 'var(--error-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Lade Fahrzeugdaten...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE97 Autohaus Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'var(--text-secondary)',\n              margin: 0\n            },\n            children: [\"Verwaltung aller \", statistics.total, \" Fahrzeuge\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              marginTop: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaGlobe, {\n              size: 14,\n              style: {\n                color: 'var(--primary-color)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '0.875rem',\n                color: 'var(--text-secondary)'\n              },\n              children: [\"Datenquelle: \", dataSource]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refreshData,\n            disabled: refreshing,\n            className: \"btn\",\n            style: {\n              backgroundColor: refreshing ? 'var(--border-color)' : 'var(--success-color)',\n              color: 'white',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaSync, {\n              style: {\n                animation: refreshing ? 'spin 1s linear infinite' : 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), refreshing ? 'Aktualisiere...' : 'Daten aktualisieren']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/management\",\n            className: \"btn\",\n            style: {\n              backgroundColor: 'var(--warning-color)',\n              color: 'white'\n            },\n            children: \"\\uD83D\\uDE97 Drag & Drop Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/vehicle/add\",\n            className: \"btn btn-primary\",\n            children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n              style: {\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), \"Fahrzeug hinzuf\\xFCgen\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-2\",\n        style: {\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n              style: {\n                color: 'var(--primary-color)',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Fahrzeug-Statistiken\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: 'var(--success-color)'\n                },\n                children: statistics.available\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Verf\\xFCgbar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: 'var(--warning-color)'\n                },\n                children: statistics.reserved\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Reserviert\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: 'var(--secondary-color)'\n                },\n                children: statistics.sold\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Verkauft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaBatteryHalf, {\n              style: {\n                color: 'var(--success-color)',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Smartcar Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                fontWeight: 'bold',\n                color: 'var(--primary-color)'\n              },\n              children: statistics.smartcarConnected\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: 'var(--text-secondary)'\n              },\n              children: \"Verbundene Fahrzeuge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/auth\",\n              className: \"btn\",\n              style: {\n                marginTop: '1rem',\n                fontSize: '0.875rem'\n              },\n              children: \"Weitere verbinden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          marginBottom: '2rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 'bold'\n              },\n              children: \"Suche\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: 'var(--text-secondary)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Marke, Modell, Farbe oder VIN...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                style: {\n                  width: '100%',\n                  padding: '0.75rem 1rem 0.75rem 2.5rem',\n                  border: '1px solid var(--border-color)',\n                  borderRadius: '6px',\n                  fontSize: '1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '0.5rem',\n                fontWeight: 'bold'\n              },\n              children: \"Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: statusFilter,\n                onChange: e => setStatusFilter(e.target.value),\n                style: {\n                  flex: 1,\n                  padding: '0.75rem',\n                  border: '1px solid var(--border-color)',\n                  borderRadius: '6px',\n                  fontSize: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"alle\",\n                  children: \"Alle Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"verf\\xFCgbar\",\n                  children: \"Verf\\xFCgbar\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"reserviert\",\n                  children: \"Reserviert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"verkauft\",\n                  children: \"Verkauft\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"werkstatt\",\n                  children: \"Werkstatt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: showSmartcarOnly,\n                  onChange: e => setShowSmartcarOnly(e.target.checked)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), \"Nur Smartcar\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-3\",\n      children: filteredVehicles.map(vehicle => {\n        var _vehicle$price, _ref;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaCar, {\n              size: 20,\n              style: {\n                color: 'var(--primary-color)',\n                marginRight: '0.5rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.1rem'\n                },\n                children: vehicle.make\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: vehicle.model\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'right'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.875rem',\n                  fontWeight: 'bold',\n                  color: getStatusColor(vehicle.status)\n                },\n                children: vehicle.status.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.75rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: vehicle.year\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Preis:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [(_vehicle$price = vehicle.price) === null || _vehicle$price === void 0 ? void 0 : _vehicle$price.toLocaleString('de-DE'), \" \\u20AC\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"KM:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [(_ref = vehicle.mileage || vehicle.km) === null || _ref === void 0 ? void 0 : _ref.toLocaleString('de-DE'), \" km\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Kraftstoff:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: vehicle.fuelType || vehicle.fuel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem',\n                  color: 'var(--text-secondary)'\n                },\n                children: \"Standort:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '0.875rem'\n                },\n                children: vehicle.location\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), vehicle.smartcarConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                marginTop: '0.5rem',\n                padding: '0.5rem',\n                backgroundColor: 'var(--success-color)',\n                color: 'white',\n                borderRadius: '4px',\n                fontSize: '0.875rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaBatteryHalf, {\n                style: {\n                  marginRight: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), \"Smartcar verbunden \", vehicle.battery && `(${vehicle.battery}%)`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/vehicle/${vehicle.id}`,\n            className: \"btn btn-primary\",\n            style: {\n              width: '100%',\n              textAlign: 'center',\n              textDecoration: 'none'\n            },\n            children: \"Details anzeigen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)]\n        }, vehicle.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), filteredVehicles.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      style: {\n        textAlign: 'center',\n        gridColumn: '1 / -1'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Keine Fahrzeuge gefunden\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: 'var(--text-secondary)',\n          margin: '1rem 0'\n        },\n        children: searchQuery || statusFilter !== 'alle' || showSmartcarOnly ? 'Keine Fahrzeuge entsprechen den aktuellen Filterkriterien.' : 'Noch keine Fahrzeuge im System.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), !searchQuery && statusFilter === 'alle' && !showSmartcarOnly && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/vehicle/add\",\n        className: \"btn btn-primary\",\n        children: \"Erstes Fahrzeug hinzuf\\xFCgen\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n}\n_s(Dashboard, \"LPtRk7Sc2vGavM6NPjWDLDFQ9QU=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Link", "FaCar", "FaBatteryHalf", "FaPlus", "FaSearch", "FaChartBar", "FaSync", "FaGlobe", "realVehicleService", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "vehicles", "setVehicles", "filteredVehicles", "setFilteredVehicles", "loading", "setLoading", "refreshing", "setRefreshing", "searchQuery", "setSearch<PERSON>uery", "statusFilter", "setStatus<PERSON>ilter", "statistics", "setStatistics", "showSmartcarOnly", "setShowSmartcarOnly", "dataSource", "setDataSource", "loadVehicles", "console", "log", "allVehicles", "getAllVehicles", "stats", "getStatistics", "length", "_allVehicles$0$id", "_allVehicles$0$id2", "_allVehicles$0$id3", "id", "startsWith", "error", "filterVehicles", "filtered", "searchVehicles", "filter", "v", "status", "smartcarConnected", "refreshData", "getStatusColor", "className", "style", "padding", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "display", "justifyContent", "alignItems", "color", "margin", "total", "gap", "marginTop", "size", "fontSize", "onClick", "disabled", "backgroundColor", "animation", "to", "marginRight", "fontWeight", "available", "reserved", "sold", "position", "left", "top", "transform", "type", "placeholder", "value", "onChange", "e", "target", "width", "border", "borderRadius", "flex", "whiteSpace", "checked", "map", "vehicle", "_vehicle$price", "_ref", "make", "model", "toUpperCase", "year", "price", "toLocaleString", "mileage", "km", "fuelType", "fuel", "location", "battery", "textDecoration", "gridColumn", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaCar, FaBatteryHalf, FaPlus, FaSearch, FaChartBar, FaSync, FaGlobe } from 'react-icons/fa';\nimport realVehicleService from '../services/realVehicleService';\n\nfunction Dashboard() {\n  const [vehicles, setVehicles] = useState([]);\n  const [filteredVehicles, setFilteredVehicles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState('alle');\n  const [statistics, setStatistics] = useState({});\n  const [showSmartcarOnly, setShowSmartcarOnly] = useState(false);\n  const [dataSource, setDataSource] = useState('loading...');\n\n  useEffect(() => {\n    loadVehicles();\n  }, []);\n\n  const loadVehicles = async () => {\n    try {\n      setLoading(true);\n      console.log('🚗 Lade echte Fahrzeugdaten...');\n\n      // Lade Fahrzeuge von automobile-nord.com\n      const allVehicles = await realVehicleService.getAllVehicles();\n      const stats = realVehicleService.getStatistics();\n\n      setVehicles(allVehicles);\n      setStatistics(stats);\n\n      // Bestimme Datenquelle\n      if (allVehicles.length > 0) {\n        if (allVehicles[0].id?.startsWith('fb-')) {\n          setDataSource('Fallback-Daten (Backend nicht erreichbar)');\n        } else if (allVehicles[0].id?.startsWith('an-')) {\n          setDataSource('automobile-nord.com (simuliert)');\n        } else if (allVehicles[0].id?.startsWith('real-')) {\n          setDataSource('automobile-nord.com (echt)');\n        } else {\n          setDataSource('Backend API');\n        }\n      } else {\n        setDataSource('Keine Daten verfügbar');\n      }\n\n      console.log(`✅ ${allVehicles.length} Fahrzeuge geladen von: ${dataSource}`);\n    } catch (error) {\n      console.error('❌ Fehler beim Laden der Fahrzeuge:', error);\n      setDataSource('Fehler beim Laden');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterVehicles = useCallback(() => {\n    let filtered = vehicles;\n\n    // Suchfilter\n    if (searchQuery) {\n      filtered = realVehicleService.searchVehicles(filtered, searchQuery);\n    }\n\n    // Status-Filter\n    if (statusFilter !== 'alle') {\n      filtered = filtered.filter(v => v.status === statusFilter);\n    }\n\n    // Smartcar-Filter\n    if (showSmartcarOnly) {\n      filtered = filtered.filter(v => v.smartcarConnected);\n    }\n\n    setFilteredVehicles(filtered);\n  }, [vehicles, searchQuery, statusFilter, showSmartcarOnly]);\n\n  useEffect(() => {\n    filterVehicles();\n  }, [filterVehicles]);\n\n  // Daten manuell aktualisieren\n  const refreshData = async () => {\n    try {\n      setRefreshing(true);\n      console.log('🔄 Aktualisiere Fahrzeugdaten...');\n\n      const allVehicles = await realVehicleService.refreshData();\n      const stats = realVehicleService.getStatistics();\n\n      setVehicles(allVehicles);\n      setStatistics(stats);\n\n      console.log(`✅ ${allVehicles.length} Fahrzeuge aktualisiert`);\n    } catch (error) {\n      console.error('❌ Fehler beim Aktualisieren:', error);\n    } finally {\n      setRefreshing(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'verfügbar': return 'var(--success-color)';\n      case 'verkauft': return 'var(--secondary-color)';\n      case 'reserviert': return 'var(--warning-color)';\n      case 'werkstatt': return 'var(--error-color)';\n      default: return 'var(--text-secondary)';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\" style={{ padding: '2rem', textAlign: 'center' }}>\n        <h2>Lade Fahrzeugdaten...</h2>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\" style={{ padding: '2rem' }}>\n      <header style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n          <div>\n            <h1>🚗 Autohaus Dashboard</h1>\n            <p style={{ color: 'var(--text-secondary)', margin: 0 }}>\n              Verwaltung aller {statistics.total} Fahrzeuge\n            </p>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginTop: '0.5rem' }}>\n              <FaGlobe size={14} style={{ color: 'var(--primary-color)' }} />\n              <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\n                Datenquelle: {dataSource}\n              </span>\n            </div>\n          </div>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={refreshData}\n              disabled={refreshing}\n              className=\"btn\"\n              style={{\n                backgroundColor: refreshing ? 'var(--border-color)' : 'var(--success-color)',\n                color: 'white',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <FaSync style={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />\n              {refreshing ? 'Aktualisiere...' : 'Daten aktualisieren'}\n            </button>\n            <Link to=\"/management\" className=\"btn\" style={{ backgroundColor: 'var(--warning-color)', color: 'white' }}>\n              🚗 Drag & Drop Management\n            </Link>\n            <Link to=\"/vehicle/add\" className=\"btn btn-primary\">\n              <FaPlus style={{ marginRight: '0.5rem' }} />\n              Fahrzeug hinzufügen\n            </Link>\n          </div>\n        </div>\n        \n        {/* Statistiken */}\n        <div className=\"grid grid-2\" style={{ marginBottom: '2rem' }}>\n          <div className=\"card\">\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n              <FaChartBar style={{ color: 'var(--primary-color)', marginRight: '0.5rem' }} />\n              <h3>Fahrzeug-Statistiken</h3>\n            </div>\n            <div className=\"grid grid-3\">\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--success-color)' }}>{statistics.available}</div>\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verfügbar</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--warning-color)' }}>{statistics.reserved}</div>\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Reserviert</div>\n              </div>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--secondary-color)' }}>{statistics.sold}</div>\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verkauft</div>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"card\">\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n              <FaBatteryHalf style={{ color: 'var(--success-color)', marginRight: '0.5rem' }} />\n              <h3>Smartcar Integration</h3>\n            </div>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>{statistics.smartcarConnected}</div>\n              <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Verbundene Fahrzeuge</div>\n              <Link to=\"/auth\" className=\"btn\" style={{ marginTop: '1rem', fontSize: '0.875rem' }}>\n                Weitere verbinden\n              </Link>\n            </div>\n          </div>\n        </div>\n        \n        {/* Filter und Suche */}\n        <div className=\"card\" style={{ marginBottom: '2rem' }}>\n          <div className=\"grid grid-2\">\n            <div>\n              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>Suche</label>\n              <div style={{ position: 'relative' }}>\n                <FaSearch style={{ position: 'absolute', left: '1rem', top: '50%', transform: 'translateY(-50%)', color: 'var(--text-secondary)' }} />\n                <input\n                  type=\"text\"\n                  placeholder=\"Marke, Modell, Farbe oder VIN...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  style={{\n                    width: '100%',\n                    padding: '0.75rem 1rem 0.75rem 2.5rem',\n                    border: '1px solid var(--border-color)',\n                    borderRadius: '6px',\n                    fontSize: '1rem'\n                  }}\n                />\n              </div>\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>Filter</label>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <select\n                  value={statusFilter}\n                  onChange={(e) => setStatusFilter(e.target.value)}\n                  style={{\n                    flex: 1,\n                    padding: '0.75rem',\n                    border: '1px solid var(--border-color)',\n                    borderRadius: '6px',\n                    fontSize: '1rem'\n                  }}\n                >\n                  <option value=\"alle\">Alle Status</option>\n                  <option value=\"verfügbar\">Verfügbar</option>\n                  <option value=\"reserviert\">Reserviert</option>\n                  <option value=\"verkauft\">Verkauft</option>\n                  <option value=\"werkstatt\">Werkstatt</option>\n                </select>\n                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', whiteSpace: 'nowrap' }}>\n                  <input\n                    type=\"checkbox\"\n                    checked={showSmartcarOnly}\n                    onChange={(e) => setShowSmartcarOnly(e.target.checked)}\n                  />\n                  Nur Smartcar\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"grid grid-3\">\n        {filteredVehicles.map(vehicle => (\n          <div key={vehicle.id} className=\"card\">\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n              <FaCar size={20} style={{ color: 'var(--primary-color)', marginRight: '0.5rem' }} />\n              <div style={{ flex: 1 }}>\n                <h3 style={{ margin: 0, fontSize: '1.1rem' }}>{vehicle.make}</h3>\n                <div style={{ fontSize: '0.9rem', color: 'var(--text-secondary)' }}>{vehicle.model}</div>\n              </div>\n              <div style={{ textAlign: 'right' }}>\n                <div style={{ fontSize: '0.875rem', fontWeight: 'bold', color: getStatusColor(vehicle.status) }}>\n                  {vehicle.status.toUpperCase()}\n                </div>\n                <div style={{ fontSize: '0.75rem', color: 'var(--text-secondary)' }}>{vehicle.year}</div>\n              </div>\n            </div>\n\n            <div style={{ marginBottom: '1rem' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Preis:</span>\n                <span style={{ fontWeight: 'bold' }}>{vehicle.price?.toLocaleString('de-DE')} €</span>\n              </div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>KM:</span>\n                <span>{(vehicle.mileage || vehicle.km)?.toLocaleString('de-DE')} km</span>\n              </div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Kraftstoff:</span>\n                <span>{vehicle.fuelType || vehicle.fuel}</span>\n              </div>\n              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>\n                <span style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>Standort:</span>\n                <span style={{ fontSize: '0.875rem' }}>{vehicle.location}</span>\n              </div>\n              {vehicle.smartcarConnected && (\n                <div style={{ display: 'flex', alignItems: 'center', marginTop: '0.5rem', padding: '0.5rem', backgroundColor: 'var(--success-color)', color: 'white', borderRadius: '4px', fontSize: '0.875rem' }}>\n                  <FaBatteryHalf style={{ marginRight: '0.5rem' }} />\n                  Smartcar verbunden {vehicle.battery && `(${vehicle.battery}%)`}\n                </div>\n              )}\n            </div>\n\n            <Link \n              to={`/vehicle/${vehicle.id}`} \n              className=\"btn btn-primary\" \n              style={{ width: '100%', textAlign: 'center', textDecoration: 'none' }}\n            >\n              Details anzeigen\n            </Link>\n          </div>\n        ))}\n      </div>\n\n      {filteredVehicles.length === 0 && (\n        <div className=\"card\" style={{ textAlign: 'center', gridColumn: '1 / -1' }}>\n          <h3>Keine Fahrzeuge gefunden</h3>\n          <p style={{ color: 'var(--text-secondary)', margin: '1rem 0' }}>\n            {searchQuery || statusFilter !== 'alle' || showSmartcarOnly \n              ? 'Keine Fahrzeuge entsprechen den aktuellen Filterkriterien.' \n              : 'Noch keine Fahrzeuge im System.'}\n          </p>\n          {(!searchQuery && statusFilter === 'alle' && !showSmartcarOnly) && (\n            <Link to=\"/vehicle/add\" className=\"btn btn-primary\">\n              Erstes Fahrzeug hinzufügen\n            </Link>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,KAAK,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AACpG,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC;EACxD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,YAAY,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACdiC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBc,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMC,WAAW,GAAG,MAAM1B,kBAAkB,CAAC2B,cAAc,CAAC,CAAC;MAC7D,MAAMC,KAAK,GAAG5B,kBAAkB,CAAC6B,aAAa,CAAC,CAAC;MAEhDvB,WAAW,CAACoB,WAAW,CAAC;MACxBR,aAAa,CAACU,KAAK,CAAC;;MAEpB;MACA,IAAIF,WAAW,CAACI,MAAM,GAAG,CAAC,EAAE;QAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;QAC1B,KAAAF,iBAAA,GAAIL,WAAW,CAAC,CAAC,CAAC,CAACQ,EAAE,cAAAH,iBAAA,eAAjBA,iBAAA,CAAmBI,UAAU,CAAC,KAAK,CAAC,EAAE;UACxCb,aAAa,CAAC,2CAA2C,CAAC;QAC5D,CAAC,MAAM,KAAAU,kBAAA,GAAIN,WAAW,CAAC,CAAC,CAAC,CAACQ,EAAE,cAAAF,kBAAA,eAAjBA,kBAAA,CAAmBG,UAAU,CAAC,KAAK,CAAC,EAAE;UAC/Cb,aAAa,CAAC,iCAAiC,CAAC;QAClD,CAAC,MAAM,KAAAW,kBAAA,GAAIP,WAAW,CAAC,CAAC,CAAC,CAACQ,EAAE,cAAAD,kBAAA,eAAjBA,kBAAA,CAAmBE,UAAU,CAAC,OAAO,CAAC,EAAE;UACjDb,aAAa,CAAC,4BAA4B,CAAC;QAC7C,CAAC,MAAM;UACLA,aAAa,CAAC,aAAa,CAAC;QAC9B;MACF,CAAC,MAAM;QACLA,aAAa,CAAC,uBAAuB,CAAC;MACxC;MAEAE,OAAO,CAACC,GAAG,CAAC,KAAKC,WAAW,CAACI,MAAM,2BAA2BT,UAAU,EAAE,CAAC;IAC7E,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1Dd,aAAa,CAAC,mBAAmB,CAAC;IACpC,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,cAAc,GAAG9C,WAAW,CAAC,MAAM;IACvC,IAAI+C,QAAQ,GAAGjC,QAAQ;;IAEvB;IACA,IAAIQ,WAAW,EAAE;MACfyB,QAAQ,GAAGtC,kBAAkB,CAACuC,cAAc,CAACD,QAAQ,EAAEzB,WAAW,CAAC;IACrE;;IAEA;IACA,IAAIE,YAAY,KAAK,MAAM,EAAE;MAC3BuB,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK3B,YAAY,CAAC;IAC5D;;IAEA;IACA,IAAII,gBAAgB,EAAE;MACpBmB,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB,CAAC;IACtD;IAEAnC,mBAAmB,CAAC8B,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACjC,QAAQ,EAAEQ,WAAW,EAAEE,YAAY,EAAEI,gBAAgB,CAAC,CAAC;EAE3D7B,SAAS,CAAC,MAAM;IACd+C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMO,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFhC,aAAa,CAAC,IAAI,CAAC;MACnBY,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/C,MAAMC,WAAW,GAAG,MAAM1B,kBAAkB,CAAC4C,WAAW,CAAC,CAAC;MAC1D,MAAMhB,KAAK,GAAG5B,kBAAkB,CAAC6B,aAAa,CAAC,CAAC;MAEhDvB,WAAW,CAACoB,WAAW,CAAC;MACxBR,aAAa,CAACU,KAAK,CAAC;MAEpBJ,OAAO,CAACC,GAAG,CAAC,KAAKC,WAAW,CAACI,MAAM,yBAAyB,CAAC;IAC/D,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRxB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMiC,cAAc,GAAIH,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,sBAAsB;MAC/C,KAAK,UAAU;QAAE,OAAO,wBAAwB;MAChD,KAAK,YAAY;QAAE,OAAO,sBAAsB;MAChD,KAAK,WAAW;QAAE,OAAO,oBAAoB;MAC7C;QAAS,OAAO,uBAAuB;IACzC;EACF,CAAC;EAED,IAAIjC,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACzEhD,OAAA;QAAAgD,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACEpD,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBACpDhD,OAAA;MAAQ6C,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACtChD,OAAA;QAAK6C,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEH,YAAY,EAAE;QAAO,CAAE;QAAAL,QAAA,gBAC3GhD,OAAA;UAAAgD,QAAA,gBACEhD,OAAA;YAAAgD,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BpD,OAAA;YAAG6C,KAAK,EAAE;cAAEY,KAAK,EAAE,uBAAuB;cAAEC,MAAM,EAAE;YAAE,CAAE;YAAAV,QAAA,GAAC,mBACtC,EAACjC,UAAU,CAAC4C,KAAK,EAAC,YACrC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpD,OAAA;YAAK6C,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAb,QAAA,gBACxFhD,OAAA,CAACH,OAAO;cAACiE,IAAI,EAAE,EAAG;cAACjB,KAAK,EAAE;gBAAEY,KAAK,EAAE;cAAuB;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/DpD,OAAA;cAAM6C,KAAK,EAAE;gBAAEkB,QAAQ,EAAE,UAAU;gBAAEN,KAAK,EAAE;cAAwB,CAAE;cAAAT,QAAA,GAAC,eACxD,EAAC7B,UAAU;YAAA;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpD,OAAA;UAAK6C,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBAC3ChD,OAAA;YACEgE,OAAO,EAAEtB,WAAY;YACrBuB,QAAQ,EAAExD,UAAW;YACrBmC,SAAS,EAAC,KAAK;YACfC,KAAK,EAAE;cACLqB,eAAe,EAAEzD,UAAU,GAAG,qBAAqB,GAAG,sBAAsB;cAC5EgD,KAAK,EAAE,OAAO;cACdH,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE;YACP,CAAE;YAAAZ,QAAA,gBAEFhD,OAAA,CAACJ,MAAM;cAACiD,KAAK,EAAE;gBAAEsB,SAAS,EAAE1D,UAAU,GAAG,yBAAyB,GAAG;cAAO;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChF3C,UAAU,GAAG,iBAAiB,GAAG,qBAAqB;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACTpD,OAAA,CAACV,IAAI;YAAC8E,EAAE,EAAC,aAAa;YAACxB,SAAS,EAAC,KAAK;YAACC,KAAK,EAAE;cAAEqB,eAAe,EAAE,sBAAsB;cAAET,KAAK,EAAE;YAAQ,CAAE;YAAAT,QAAA,EAAC;UAE3G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpD,OAAA,CAACV,IAAI;YAAC8E,EAAE,EAAC,cAAc;YAACxB,SAAS,EAAC,iBAAiB;YAAAI,QAAA,gBACjDhD,OAAA,CAACP,MAAM;cAACoD,KAAK,EAAE;gBAAEwB,WAAW,EAAE;cAAS;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0BAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpD,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAACC,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAL,QAAA,gBAC3DhD,OAAA;UAAK4C,SAAS,EAAC,MAAM;UAAAI,QAAA,gBACnBhD,OAAA;YAAK6C,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAL,QAAA,gBAC1EhD,OAAA,CAACL,UAAU;cAACkD,KAAK,EAAE;gBAAEY,KAAK,EAAE,sBAAsB;gBAAEY,WAAW,EAAE;cAAS;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/EpD,OAAA;cAAAgD,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNpD,OAAA;YAAK4C,SAAS,EAAC,aAAa;YAAAI,QAAA,gBAC1BhD,OAAA;cAAK6C,KAAK,EAAE;gBAAEE,SAAS,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAClChD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAuB,CAAE;gBAAAT,QAAA,EAAEjC,UAAU,CAACwD;cAAS;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjHpD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAEE,SAAS,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAClChD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAuB,CAAE;gBAAAT,QAAA,EAAEjC,UAAU,CAACyD;cAAQ;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChHpD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAEE,SAAS,EAAE;cAAS,CAAE;cAAAC,QAAA,gBAClChD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAyB,CAAE;gBAAAT,QAAA,EAAEjC,UAAU,CAAC0D;cAAI;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9GpD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAK4C,SAAS,EAAC,MAAM;UAAAI,QAAA,gBACnBhD,OAAA;YAAK6C,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAL,QAAA,gBAC1EhD,OAAA,CAACR,aAAa;cAACqD,KAAK,EAAE;gBAAEY,KAAK,EAAE,sBAAsB;gBAAEY,WAAW,EAAE;cAAS;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFpD,OAAA;cAAAgD,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNpD,OAAA;YAAK6C,KAAK,EAAE;cAAEE,SAAS,EAAE;YAAS,CAAE;YAAAC,QAAA,gBAClChD,OAAA;cAAK6C,KAAK,EAAE;gBAAEkB,QAAQ,EAAE,MAAM;gBAAEO,UAAU,EAAE,MAAM;gBAAEb,KAAK,EAAE;cAAuB,CAAE;cAAAT,QAAA,EAAEjC,UAAU,CAAC0B;YAAiB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzHpD,OAAA;cAAK6C,KAAK,EAAE;gBAAEkB,QAAQ,EAAE,UAAU;gBAAEN,KAAK,EAAE;cAAwB,CAAE;cAAAT,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChGpD,OAAA,CAACV,IAAI;cAAC8E,EAAE,EAAC,OAAO;cAACxB,SAAS,EAAC,KAAK;cAACC,KAAK,EAAE;gBAAEgB,SAAS,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAW,CAAE;cAAAf,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpD,OAAA;QAAK4C,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAAEQ,YAAY,EAAE;QAAO,CAAE;QAAAL,QAAA,eACpDhD,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAI,QAAA,gBAC1BhD,OAAA;YAAAgD,QAAA,gBACEhD,OAAA;cAAO6C,KAAK,EAAE;gBAAES,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,QAAQ;gBAAEiB,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7FpD,OAAA;cAAK6C,KAAK,EAAE;gBAAE6B,QAAQ,EAAE;cAAW,CAAE;cAAA1B,QAAA,gBACnChD,OAAA,CAACN,QAAQ;gBAACmD,KAAK,EAAE;kBAAE6B,QAAQ,EAAE,UAAU;kBAAEC,IAAI,EAAE,MAAM;kBAAEC,GAAG,EAAE,KAAK;kBAAEC,SAAS,EAAE,kBAAkB;kBAAEpB,KAAK,EAAE;gBAAwB;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtIpD,OAAA;gBACE8E,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,kCAAkC;gBAC9CC,KAAK,EAAErE,WAAY;gBACnBsE,QAAQ,EAAGC,CAAC,IAAKtE,cAAc,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDnC,KAAK,EAAE;kBACLuC,KAAK,EAAE,MAAM;kBACbtC,OAAO,EAAE,6BAA6B;kBACtCuC,MAAM,EAAE,+BAA+B;kBACvCC,YAAY,EAAE,KAAK;kBACnBvB,QAAQ,EAAE;gBACZ;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpD,OAAA;YAAAgD,QAAA,gBACEhD,OAAA;cAAO6C,KAAK,EAAE;gBAAES,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,QAAQ;gBAAEiB,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9FpD,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEM,GAAG,EAAE;cAAO,CAAE;cAAAZ,QAAA,gBAC3ChD,OAAA;gBACEgF,KAAK,EAAEnE,YAAa;gBACpBoE,QAAQ,EAAGC,CAAC,IAAKpE,eAAe,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACjDnC,KAAK,EAAE;kBACL0C,IAAI,EAAE,CAAC;kBACPzC,OAAO,EAAE,SAAS;kBAClBuC,MAAM,EAAE,+BAA+B;kBACvCC,YAAY,EAAE,KAAK;kBACnBvB,QAAQ,EAAE;gBACZ,CAAE;gBAAAf,QAAA,gBAEFhD,OAAA;kBAAQgF,KAAK,EAAC,MAAM;kBAAAhC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACzCpD,OAAA;kBAAQgF,KAAK,EAAC,cAAW;kBAAAhC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CpD,OAAA;kBAAQgF,KAAK,EAAC,YAAY;kBAAAhC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CpD,OAAA;kBAAQgF,KAAK,EAAC,UAAU;kBAAAhC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CpD,OAAA;kBAAQgF,KAAK,EAAC,WAAW;kBAAAhC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACTpD,OAAA;gBAAO6C,KAAK,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEI,GAAG,EAAE,QAAQ;kBAAE4B,UAAU,EAAE;gBAAS,CAAE;gBAAAxC,QAAA,gBAC3FhD,OAAA;kBACE8E,IAAI,EAAC,UAAU;kBACfW,OAAO,EAAExE,gBAAiB;kBAC1BgE,QAAQ,EAAGC,CAAC,IAAKhE,mBAAmB,CAACgE,CAAC,CAACC,MAAM,CAACM,OAAO;gBAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,gBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETpD,OAAA;MAAK4C,SAAS,EAAC,aAAa;MAAAI,QAAA,EACzB3C,gBAAgB,CAACqF,GAAG,CAACC,OAAO;QAAA,IAAAC,cAAA,EAAAC,IAAA;QAAA,oBAC3B7F,OAAA;UAAsB4C,SAAS,EAAC,MAAM;UAAAI,QAAA,gBACpChD,OAAA;YAAK6C,KAAK,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEH,YAAY,EAAE;YAAO,CAAE;YAAAL,QAAA,gBAC1EhD,OAAA,CAACT,KAAK;cAACuE,IAAI,EAAE,EAAG;cAACjB,KAAK,EAAE;gBAAEY,KAAK,EAAE,sBAAsB;gBAAEY,WAAW,EAAE;cAAS;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpFpD,OAAA;cAAK6C,KAAK,EAAE;gBAAE0C,IAAI,EAAE;cAAE,CAAE;cAAAvC,QAAA,gBACtBhD,OAAA;gBAAI6C,KAAK,EAAE;kBAAEa,MAAM,EAAE,CAAC;kBAAEK,QAAQ,EAAE;gBAAS,CAAE;gBAAAf,QAAA,EAAE2C,OAAO,CAACG;cAAI;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjEpD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,QAAQ;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAE2C,OAAO,CAACI;cAAK;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAEE,SAAS,EAAE;cAAQ,CAAE;cAAAC,QAAA,gBACjChD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEO,UAAU,EAAE,MAAM;kBAAEb,KAAK,EAAEd,cAAc,CAACgD,OAAO,CAACnD,MAAM;gBAAE,CAAE;gBAAAQ,QAAA,EAC7F2C,OAAO,CAACnD,MAAM,CAACwD,WAAW,CAAC;cAAC;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNpD,OAAA;gBAAK6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,SAAS;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAE2C,OAAO,CAACM;cAAI;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAK6C,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAO,CAAE;YAAAL,QAAA,gBACnChD,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAL,QAAA,gBACvFhD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpFpD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEyB,UAAU,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,IAAA4C,cAAA,GAAED,OAAO,CAACO,KAAK,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,cAAc,CAAC,OAAO,CAAC,EAAC,SAAE;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAL,QAAA,gBACvFhD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFpD,OAAA;gBAAAgD,QAAA,IAAA6C,IAAA,GAAQF,OAAO,CAACS,OAAO,IAAIT,OAAO,CAACU,EAAE,cAAAR,IAAA,uBAA9BA,IAAA,CAAiCM,cAAc,CAAC,OAAO,CAAC,EAAC,KAAG;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAL,QAAA,gBACvFhD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzFpD,OAAA;gBAAAgD,QAAA,EAAO2C,OAAO,CAACW,QAAQ,IAAIX,OAAO,CAACY;cAAI;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNpD,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEF,YAAY,EAAE;cAAS,CAAE;cAAAL,QAAA,gBACvFhD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,UAAU;kBAAEN,KAAK,EAAE;gBAAwB,CAAE;gBAAAT,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvFpD,OAAA;gBAAM6C,KAAK,EAAE;kBAAEkB,QAAQ,EAAE;gBAAW,CAAE;gBAAAf,QAAA,EAAE2C,OAAO,CAACa;cAAQ;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,EACLuC,OAAO,CAAClD,iBAAiB,iBACxBzC,OAAA;cAAK6C,KAAK,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEK,SAAS,EAAE,QAAQ;gBAAEf,OAAO,EAAE,QAAQ;gBAAEoB,eAAe,EAAE,sBAAsB;gBAAET,KAAK,EAAE,OAAO;gBAAE6B,YAAY,EAAE,KAAK;gBAAEvB,QAAQ,EAAE;cAAW,CAAE;cAAAf,QAAA,gBAChMhD,OAAA,CAACR,aAAa;gBAACqD,KAAK,EAAE;kBAAEwB,WAAW,EAAE;gBAAS;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAChC,EAACuC,OAAO,CAACc,OAAO,IAAI,IAAId,OAAO,CAACc,OAAO,IAAI;YAAA;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENpD,OAAA,CAACV,IAAI;YACH8E,EAAE,EAAE,YAAYuB,OAAO,CAAC3D,EAAE,EAAG;YAC7BY,SAAS,EAAC,iBAAiB;YAC3BC,KAAK,EAAE;cAAEuC,KAAK,EAAE,MAAM;cAAErC,SAAS,EAAE,QAAQ;cAAE2D,cAAc,EAAE;YAAO,CAAE;YAAA1D,QAAA,EACvE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GA9CCuC,OAAO,CAAC3D,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+Cf,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL/C,gBAAgB,CAACuB,MAAM,KAAK,CAAC,iBAC5B5B,OAAA;MAAK4C,SAAS,EAAC,MAAM;MAACC,KAAK,EAAE;QAAEE,SAAS,EAAE,QAAQ;QAAE4D,UAAU,EAAE;MAAS,CAAE;MAAA3D,QAAA,gBACzEhD,OAAA;QAAAgD,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCpD,OAAA;QAAG6C,KAAK,EAAE;UAAEY,KAAK,EAAE,uBAAuB;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAC5DrC,WAAW,IAAIE,YAAY,KAAK,MAAM,IAAII,gBAAgB,GACvD,4DAA4D,GAC5D;MAAiC;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EACF,CAACzC,WAAW,IAAIE,YAAY,KAAK,MAAM,IAAI,CAACI,gBAAgB,iBAC5DjB,OAAA,CAACV,IAAI;QAAC8E,EAAE,EAAC,cAAc;QAACxB,SAAS,EAAC,iBAAiB;QAAAI,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAClD,EAAA,CAhUQD,SAAS;AAAA2G,EAAA,GAAT3G,SAAS;AAkUlB,eAAeA,SAAS;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}