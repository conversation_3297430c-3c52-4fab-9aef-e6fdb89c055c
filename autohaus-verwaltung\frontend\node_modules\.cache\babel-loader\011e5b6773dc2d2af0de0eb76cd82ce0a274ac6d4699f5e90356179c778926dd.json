{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\vceeeeeettttttt\\\\autohaus-verwaltung\\\\frontend\\\\src\\\\pages\\\\VehicleDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { FaArrowLeft, FaBatteryHalf, FaMapMarkerAlt, FaTachometerAlt, FaWrench } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VehicleDetails() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [vehicle, setVehicle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadVehicleDetails();\n  }, [id]);\n  const loadVehicleDetails = async () => {\n    try {\n      // Für Demo verwenden wir Mock-Daten\n      setTimeout(() => {\n        setVehicle({\n          id: id,\n          make: 'Tesla',\n          model: 'Model 3',\n          year: 2023,\n          battery: 85,\n          location: 'Hamburg, Deutschland',\n          odometer: 15420,\n          tirePressure: {\n            frontLeft: 2.3,\n            frontRight: 2.2,\n            rearLeft: 2.4,\n            rearRight: 2.3\n          },\n          lastUpdate: new Date().toISOString()\n        });\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('Fehler beim Laden der Fahrzeugdaten');\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Lade Fahrzeugdetails...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      style: {\n        padding: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: 'var(--error-color)'\n          },\n          children: \"Fehler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-primary\",\n          children: \"Zur\\xFCck zum Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        style: {\n          marginRight: '1rem',\n          color: 'var(--primary-color)'\n        },\n        children: /*#__PURE__*/_jsxDEV(FaArrowLeft, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [vehicle.make, \" \", vehicle.model]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            margin: 0\n          },\n          children: [\"Baujahr \", vehicle.year, \" \\u2022 Letzte Aktualisierung: \", new Date(vehicle.lastUpdate).toLocaleString('de-DE')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaBatteryHalf, {\n            style: {\n              color: 'var(--success-color)',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Batteriestand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              fontWeight: 'bold',\n              color: 'var(--success-color)'\n            },\n            children: [vehicle.battery, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '20px',\n              backgroundColor: 'var(--border-color)',\n              borderRadius: '10px',\n              overflow: 'hidden',\n              marginTop: '1rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${vehicle.battery}%`,\n                height: '100%',\n                backgroundColor: vehicle.battery > 50 ? 'var(--success-color)' : vehicle.battery > 20 ? 'var(--warning-color)' : 'var(--error-color)',\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            style: {\n              color: 'var(--warning-color)',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Standort\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.2rem',\n              fontWeight: 'bold'\n            },\n            children: vehicle.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '1rem',\n              padding: '1rem',\n              backgroundColor: 'var(--bg-color)',\n              borderRadius: '6px',\n              color: 'var(--text-secondary)'\n            },\n            children: \"\\uD83D\\uDCCD Karte wird geladen...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaTachometerAlt, {\n            style: {\n              color: 'var(--secondary-color)',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Kilometerstand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold'\n            },\n            children: [vehicle.odometer.toLocaleString('de-DE'), \" km\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: 'var(--text-secondary)',\n              marginTop: '0.5rem'\n            },\n            children: \"Gesamtkilometer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginBottom: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaWrench, {\n            style: {\n              color: 'var(--secondary-color)',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Reifendruck\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: \"Vorne Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.2rem',\n                color: 'var(--success-color)'\n              },\n              children: [vehicle.tirePressure.frontLeft, \" bar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: \"Vorne Rechts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.2rem',\n                color: 'var(--success-color)'\n              },\n              children: [vehicle.tirePressure.frontRight, \" bar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: \"Hinten Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.2rem',\n                color: 'var(--success-color)'\n              },\n              children: [vehicle.tirePressure.rearLeft, \" bar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: \"Hinten Rechts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.2rem',\n                color: 'var(--success-color)'\n              },\n              children: [vehicle.tirePressure.rearRight, \" bar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: loadVehicleDetails,\n        style: {\n          marginRight: '1rem'\n        },\n        children: \"Daten aktualisieren\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"btn\",\n        style: {\n          backgroundColor: 'var(--secondary-color)',\n          color: 'white',\n          textDecoration: 'none'\n        },\n        children: \"Zur\\xFCck zum Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n}\n_s(VehicleDetails, \"lG8kdnj/fxKEs4NNiYRFDBPlMYc=\", false, function () {\n  return [useParams];\n});\n_c = VehicleDetails;\nexport default VehicleDetails;\nvar _c;\n$RefreshReg$(_c, \"VehicleDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "FaArrowLeft", "FaBatteryHalf", "FaMapMarkerAlt", "FaTachometerAlt", "FaWrench", "jsxDEV", "_jsxDEV", "VehicleDetails", "_s", "id", "vehicle", "setVehicle", "loading", "setLoading", "error", "setError", "loadVehicleDetails", "setTimeout", "make", "model", "year", "battery", "location", "odometer", "tirePressure", "frontLeft", "frontRight", "rearLeft", "rearRight", "lastUpdate", "Date", "toISOString", "err", "className", "style", "padding", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "to", "marginBottom", "display", "alignItems", "marginRight", "size", "margin", "toLocaleString", "fontSize", "fontWeight", "width", "height", "backgroundColor", "borderRadius", "overflow", "marginTop", "transition", "onClick", "textDecoration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/vceeeeeettttttt/autohaus-verwaltung/frontend/src/pages/VehicleDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';\nimport { FaArrowLeft, FaBatteryHalf, FaMapMarkerAlt, FaTachometerAlt, FaWrench } from 'react-icons/fa';\n\nfunction VehicleDetails() {\n  const { id } = useParams();\n  const [vehicle, setVehicle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    loadVehicleDetails();\n  }, [id]);\n\n  const loadVehicleDetails = async () => {\n    try {\n      // Für Demo verwenden wir Mock-Daten\n      setTimeout(() => {\n        setVehicle({\n          id: id,\n          make: 'Tesla',\n          model: 'Model 3',\n          year: 2023,\n          battery: 85,\n          location: 'Hamburg, Deutschland',\n          odometer: 15420,\n          tirePressure: {\n            frontLeft: 2.3,\n            frontRight: 2.2,\n            rearLeft: 2.4,\n            rearRight: 2.3\n          },\n          lastUpdate: new Date().toISOString()\n        });\n        setLoading(false);\n      }, 1000);\n    } catch (err) {\n      setError('<PERSON><PERSON> beim <PERSON> der Fahrzeugdaten');\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\" style={{ padding: '2rem', textAlign: 'center' }}>\n        <h2>Lade Fahrzeugdetails...</h2>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container\" style={{ padding: '2rem' }}>\n        <div className=\"card\" style={{ textAlign: 'center' }}>\n          <h2 style={{ color: 'var(--error-color)' }}>Fehler</h2>\n          <p>{error}</p>\n          <Link to=\"/\" className=\"btn btn-primary\">Zurück zum Dashboard</Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\" style={{ padding: '2rem' }}>\n      <header style={{ marginBottom: '2rem', display: 'flex', alignItems: 'center' }}>\n        <Link to=\"/\" style={{ marginRight: '1rem', color: 'var(--primary-color)' }}>\n          <FaArrowLeft size={20} />\n        </Link>\n        <div>\n          <h1>{vehicle.make} {vehicle.model}</h1>\n          <p style={{ color: 'var(--text-secondary)', margin: 0 }}>\n            Baujahr {vehicle.year} • Letzte Aktualisierung: {new Date(vehicle.lastUpdate).toLocaleString('de-DE')}\n          </p>\n        </div>\n      </header>\n\n      <div className=\"grid grid-2\">\n        {/* Batteriestand */}\n        <div className=\"card\">\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <FaBatteryHalf style={{ color: 'var(--success-color)', marginRight: '0.5rem' }} />\n            <h3>Batteriestand</h3>\n          </div>\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '3rem', fontWeight: 'bold', color: 'var(--success-color)' }}>\n              {vehicle.battery}%\n            </div>\n            <div style={{ \n              width: '100%', \n              height: '20px', \n              backgroundColor: 'var(--border-color)', \n              borderRadius: '10px',\n              overflow: 'hidden',\n              marginTop: '1rem'\n            }}>\n              <div style={{\n                width: `${vehicle.battery}%`,\n                height: '100%',\n                backgroundColor: vehicle.battery > 50 ? 'var(--success-color)' : \n                                vehicle.battery > 20 ? 'var(--warning-color)' : 'var(--error-color)',\n                transition: 'width 0.3s ease'\n              }}></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Standort */}\n        <div className=\"card\">\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <FaMapMarkerAlt style={{ color: 'var(--warning-color)', marginRight: '0.5rem' }} />\n            <h3>Standort</h3>\n          </div>\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>\n              {vehicle.location}\n            </div>\n            <div style={{ \n              marginTop: '1rem', \n              padding: '1rem', \n              backgroundColor: 'var(--bg-color)', \n              borderRadius: '6px',\n              color: 'var(--text-secondary)'\n            }}>\n              📍 Karte wird geladen...\n            </div>\n          </div>\n        </div>\n\n        {/* Kilometerstand */}\n        <div className=\"card\">\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <FaTachometerAlt style={{ color: 'var(--secondary-color)', marginRight: '0.5rem' }} />\n            <h3>Kilometerstand</h3>\n          </div>\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold' }}>\n              {vehicle.odometer.toLocaleString('de-DE')} km\n            </div>\n            <div style={{ color: 'var(--text-secondary)', marginTop: '0.5rem' }}>\n              Gesamtkilometer\n            </div>\n          </div>\n        </div>\n\n        {/* Reifendruck */}\n        <div className=\"card\">\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <FaWrench style={{ color: 'var(--secondary-color)', marginRight: '0.5rem' }} />\n            <h3>Reifendruck</h3>\n          </div>\n          <div className=\"grid grid-2\">\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontWeight: 'bold' }}>Vorne Links</div>\n              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>\n                {vehicle.tirePressure.frontLeft} bar\n              </div>\n            </div>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontWeight: 'bold' }}>Vorne Rechts</div>\n              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>\n                {vehicle.tirePressure.frontRight} bar\n              </div>\n            </div>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontWeight: 'bold' }}>Hinten Links</div>\n              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>\n                {vehicle.tirePressure.rearLeft} bar\n              </div>\n            </div>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontWeight: 'bold' }}>Hinten Rechts</div>\n              <div style={{ fontSize: '1.2rem', color: 'var(--success-color)' }}>\n                {vehicle.tirePressure.rearRight} bar\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div style={{ marginTop: '2rem', textAlign: 'center' }}>\n        <button \n          className=\"btn btn-primary\" \n          onClick={loadVehicleDetails}\n          style={{ marginRight: '1rem' }}\n        >\n          Daten aktualisieren\n        </button>\n        <Link to=\"/\" className=\"btn\" style={{ \n          backgroundColor: 'var(--secondary-color)', \n          color: 'white',\n          textDecoration: 'none'\n        }}>\n          Zurück zum Dashboard\n        </Link>\n      </div>\n    </div>\n  );\n}\n\nexport default VehicleDetails;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,WAAW,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvG,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdmB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACP,EAAE,CAAC,CAAC;EAER,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAC,UAAU,CAAC,MAAM;QACfN,UAAU,CAAC;UACTF,EAAE,EAAEA,EAAE;UACNS,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE,sBAAsB;UAChCC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE;YACZC,SAAS,EAAE,GAAG;YACdC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,GAAG;YACbC,SAAS,EAAE;UACb,CAAC;UACDC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,CAAC,CAAC;QACFlB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOmB,GAAG,EAAE;MACZjB,QAAQ,CAAC,qCAAqC,CAAC;MAC/CF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK2B,SAAS,EAAC,WAAW;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACzE/B,OAAA;QAAA+B,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,IAAI3B,KAAK,EAAE;IACT,oBACER,OAAA;MAAK2B,SAAS,EAAC,WAAW;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAE,QAAA,eACpD/B,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAAEE,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACnD/B,OAAA;UAAI4B,KAAK,EAAE;YAAEQ,KAAK,EAAE;UAAqB,CAAE;UAAAL,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDnC,OAAA;UAAA+B,QAAA,EAAIvB;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnC,OAAA,CAACP,IAAI;UAAC4C,EAAE,EAAC,GAAG;UAACV,SAAS,EAAC,iBAAiB;UAAAI,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK2B,SAAS,EAAC,WAAW;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBACpD/B,OAAA;MAAQ4B,KAAK,EAAE;QAAEU,YAAY,EAAE,MAAM;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAT,QAAA,gBAC7E/B,OAAA,CAACP,IAAI;QAAC4C,EAAE,EAAC,GAAG;QAACT,KAAK,EAAE;UAAEa,WAAW,EAAE,MAAM;UAAEL,KAAK,EAAE;QAAuB,CAAE;QAAAL,QAAA,eACzE/B,OAAA,CAACN,WAAW;UAACgD,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACPnC,OAAA;QAAA+B,QAAA,gBACE/B,OAAA;UAAA+B,QAAA,GAAK3B,OAAO,CAACQ,IAAI,EAAC,GAAC,EAACR,OAAO,CAACS,KAAK;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCnC,OAAA;UAAG4B,KAAK,EAAE;YAAEQ,KAAK,EAAE,uBAAuB;YAAEO,MAAM,EAAE;UAAE,CAAE;UAAAZ,QAAA,GAAC,UAC/C,EAAC3B,OAAO,CAACU,IAAI,EAAC,iCAA0B,EAAC,IAAIU,IAAI,CAACpB,OAAO,CAACmB,UAAU,CAAC,CAACqB,cAAc,CAAC,OAAO,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETnC,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAI,QAAA,gBAE1B/B,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAI,QAAA,gBACnB/B,OAAA;UAAK4B,KAAK,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC1E/B,OAAA,CAACL,aAAa;YAACiC,KAAK,EAAE;cAAEQ,KAAK,EAAE,sBAAsB;cAAEK,WAAW,EAAE;YAAS;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClFnC,OAAA;YAAA+B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNnC,OAAA;UAAK4B,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAClC/B,OAAA;YAAK4B,KAAK,EAAE;cAAEiB,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEV,KAAK,EAAE;YAAuB,CAAE;YAAAL,QAAA,GACjF3B,OAAO,CAACW,OAAO,EAAC,GACnB;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cACVmB,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,eAAe,EAAE,qBAAqB;cACtCC,YAAY,EAAE,MAAM;cACpBC,QAAQ,EAAE,QAAQ;cAClBC,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,eACA/B,OAAA;cAAK4B,KAAK,EAAE;gBACVmB,KAAK,EAAE,GAAG3C,OAAO,CAACW,OAAO,GAAG;gBAC5BiC,MAAM,EAAE,MAAM;gBACdC,eAAe,EAAE7C,OAAO,CAACW,OAAO,GAAG,EAAE,GAAG,sBAAsB,GAC9CX,OAAO,CAACW,OAAO,GAAG,EAAE,GAAG,sBAAsB,GAAG,oBAAoB;gBACpFsC,UAAU,EAAE;cACd;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAI,QAAA,gBACnB/B,OAAA;UAAK4B,KAAK,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC1E/B,OAAA,CAACJ,cAAc;YAACgC,KAAK,EAAE;cAAEQ,KAAK,EAAE,sBAAsB;cAAEK,WAAW,EAAE;YAAS;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFnC,OAAA;YAAA+B,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNnC,OAAA;UAAK4B,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAClC/B,OAAA;YAAK4B,KAAK,EAAE;cAAEiB,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAf,QAAA,EACpD3B,OAAO,CAACY;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cACVwB,SAAS,EAAE,MAAM;cACjBvB,OAAO,EAAE,MAAM;cACfoB,eAAe,EAAE,iBAAiB;cAClCC,YAAY,EAAE,KAAK;cACnBd,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAI,QAAA,gBACnB/B,OAAA;UAAK4B,KAAK,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC1E/B,OAAA,CAACH,eAAe;YAAC+B,KAAK,EAAE;cAAEQ,KAAK,EAAE,wBAAwB;cAAEK,WAAW,EAAE;YAAS;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtFnC,OAAA;YAAA+B,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACNnC,OAAA;UAAK4B,KAAK,EAAE;YAAEE,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,gBAClC/B,OAAA;YAAK4B,KAAK,EAAE;cAAEiB,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAf,QAAA,GAClD3B,OAAO,CAACa,QAAQ,CAAC2B,cAAc,CAAC,OAAO,CAAC,EAAC,KAC5C;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cAAEQ,KAAK,EAAE,uBAAuB;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAArB,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK2B,SAAS,EAAC,MAAM;QAAAI,QAAA,gBACnB/B,OAAA;UAAK4B,KAAK,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC1E/B,OAAA,CAACF,QAAQ;YAAC8B,KAAK,EAAE;cAAEQ,KAAK,EAAE,wBAAwB;cAAEK,WAAW,EAAE;YAAS;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EnC,OAAA;YAAA+B,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNnC,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAI,QAAA,gBAC1B/B,OAAA;YAAK4B,KAAK,EAAE;cAAEE,SAAS,EAAE;YAAS,CAAE;YAAAC,QAAA,gBAClC/B,OAAA;cAAK4B,KAAK,EAAE;gBAAEkB,UAAU,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrDnC,OAAA;cAAK4B,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,QAAQ;gBAAET,KAAK,EAAE;cAAuB,CAAE;cAAAL,QAAA,GAC/D3B,OAAO,CAACc,YAAY,CAACC,SAAS,EAAC,MAClC;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cAAEE,SAAS,EAAE;YAAS,CAAE;YAAAC,QAAA,gBAClC/B,OAAA;cAAK4B,KAAK,EAAE;gBAAEkB,UAAU,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDnC,OAAA;cAAK4B,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,QAAQ;gBAAET,KAAK,EAAE;cAAuB,CAAE;cAAAL,QAAA,GAC/D3B,OAAO,CAACc,YAAY,CAACE,UAAU,EAAC,MACnC;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cAAEE,SAAS,EAAE;YAAS,CAAE;YAAAC,QAAA,gBAClC/B,OAAA;cAAK4B,KAAK,EAAE;gBAAEkB,UAAU,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDnC,OAAA;cAAK4B,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,QAAQ;gBAAET,KAAK,EAAE;cAAuB,CAAE;cAAAL,QAAA,GAC/D3B,OAAO,CAACc,YAAY,CAACG,QAAQ,EAAC,MACjC;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnC,OAAA;YAAK4B,KAAK,EAAE;cAAEE,SAAS,EAAE;YAAS,CAAE;YAAAC,QAAA,gBAClC/B,OAAA;cAAK4B,KAAK,EAAE;gBAAEkB,UAAU,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDnC,OAAA;cAAK4B,KAAK,EAAE;gBAAEiB,QAAQ,EAAE,QAAQ;gBAAET,KAAK,EAAE;cAAuB,CAAE;cAAAL,QAAA,GAC/D3B,OAAO,CAACc,YAAY,CAACI,SAAS,EAAC,MAClC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnC,OAAA;MAAK4B,KAAK,EAAE;QAAEwB,SAAS,EAAE,MAAM;QAAEtB,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrD/B,OAAA;QACE2B,SAAS,EAAC,iBAAiB;QAC3B2B,OAAO,EAAE5C,kBAAmB;QAC5BkB,KAAK,EAAE;UAAEa,WAAW,EAAE;QAAO,CAAE;QAAAV,QAAA,EAChC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnC,OAAA,CAACP,IAAI;QAAC4C,EAAE,EAAC,GAAG;QAACV,SAAS,EAAC,KAAK;QAACC,KAAK,EAAE;UAClCqB,eAAe,EAAE,wBAAwB;UACzCb,KAAK,EAAE,OAAO;UACdmB,cAAc,EAAE;QAClB,CAAE;QAAAxB,QAAA,EAAC;MAEH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjC,EAAA,CAjMQD,cAAc;EAAA,QACNT,SAAS;AAAA;AAAAgE,EAAA,GADjBvD,cAAc;AAmMvB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}