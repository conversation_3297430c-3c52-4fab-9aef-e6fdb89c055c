// Mock Backend Service - Simuliert echte Daten von automobile-nord.com und mobile.de
// Wird verwendet bis das echte Backend konfiguriert ist

class MockBackendService {
  constructor() {
    this.isOnline = true;
    this.lastScrapeTime = null;
  }

  // Simuliere Gesundheitsstatus
  async checkHealth() {
    await this.delay(100);
    return {
      success: true,
      status: 'ok',
      message: 'Mock Backend läuft',
      timestamp: new Date().toISOString()
    };
  }

  // Simuliere Scraping von automobile-nord.com
  async scrapeAutomobileNord(importData = false) {
    console.log('🔄 Simuliere Scraping von automobile-nord.com...');
    await this.delay(2000); // Simuliere Netzwerkverzögerung

    const mockVehicles = this.generateRealisticVehicles();
    this.lastScrapeTime = new Date();

    return {
      success: true,
      data: mockVehicles,
      vehiclesFound: mockVehicles.length,
      source: 'automobile-nord.com (simuliert)',
      timestamp: this.lastScrapeTime.toISOString()
    };
  }

  // Simuliere mobile.de Marktdaten
  async scrapeMobileDe(searchQuery = '') {
    console.log('📊 Simuliere mobile.de Marktdaten...');
    await this.delay(1500);

    const marketData = this.generateMarketData(searchQuery);

    return {
      success: true,
      data: {
        marketData,
        count: marketData.length,
        searchQuery,
        source: 'mobile.de (simuliert)'
      }
    };
  }

  // Generiere realistische Fahrzeugdaten
  generateRealisticVehicles() {
    const vehicles = [];
    const makes = ['BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Porsche', 'Tesla', 'Ford', 'Opel'];
    const models = {
      'BMW': ['320i', '520d', 'X3', 'X5', 'i4', '330e'],
      'Mercedes-Benz': ['C-Klasse', 'E-Klasse', 'GLC', 'GLE', 'A-Klasse', 'CLA'],
      'Audi': ['A4', 'A6', 'Q5', 'Q7', 'e-tron', 'A3'],
      'Volkswagen': ['Golf', 'Passat', 'Tiguan', 'Touareg', 'ID.4', 'Arteon'],
      'Porsche': ['911', 'Cayenne', 'Macan', 'Panamera', 'Taycan'],
      'Tesla': ['Model 3', 'Model S', 'Model X', 'Model Y'],
      'Ford': ['Focus', 'Kuga', 'Mustang', 'Explorer', 'Mondeo'],
      'Opel': ['Astra', 'Insignia', 'Mokka', 'Grandland', 'Corsa']
    };
    
    const colors = ['Schwarz', 'Weiß', 'Silber', 'Grau', 'Blau', 'Rot', 'Braun'];
    const fuelTypes = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];
    const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];

    for (let i = 1; i <= 25; i++) {
      const make = makes[Math.floor(Math.random() * makes.length)];
      const modelOptions = models[make];
      const model = modelOptions[Math.floor(Math.random() * modelOptions.length)];
      const year = 2020 + Math.floor(Math.random() * 4);
      const isElectric = model.includes('e-tron') || model.includes('ID.') || model.includes('Taycan') || make === 'Tesla';
      const fuelType = isElectric ? 'Elektro' : fuelTypes[Math.floor(Math.random() * fuelTypes.length)];
      
      // Realistische Preise basierend auf Marke und Jahr
      let basePrice = 25000;
      if (make === 'Porsche') basePrice = 80000;
      else if (make === 'BMW' || make === 'Mercedes-Benz') basePrice = 45000;
      else if (make === 'Audi') basePrice = 40000;
      else if (make === 'Tesla') basePrice = 50000;
      
      const price = basePrice + Math.floor(Math.random() * 30000) + (year - 2020) * 5000;
      const mileage = Math.floor(Math.random() * 80000) + 5000;
      
      vehicles.push({
        id: `an-${i.toString().padStart(3, '0')}`,
        make,
        model,
        year,
        price,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        type: this.getVehicleType(model),
        fuelType,
        mileage,
        color: colors[Math.floor(Math.random() * colors.length)],
        vin: `${make.substring(0, 3).toUpperCase()}${i.toString().padStart(9, '0')}`,
        location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i/10))}${(i%10)+1}`,
        smartcarConnected: isElectric && Math.random() < 0.7, // 70% der E-Autos haben Smartcar
        battery: isElectric ? Math.floor(Math.random() * 40) + 60 : null,
        description: `Gepflegter ${make} ${model} in ${colors[Math.floor(Math.random() * colors.length)]}. Aus erster Hand.`,
        features: this.generateFeatures(make, model),
        image: null,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        source: 'automobile-nord.com'
      });
    }

    return vehicles;
  }

  // Bestimme Fahrzeugtyp basierend auf Modell
  getVehicleType(model) {
    if (model.includes('X') || model.includes('Q') || model.includes('GLC') || model.includes('GLE') || 
        model.includes('Tiguan') || model.includes('Cayenne') || model.includes('Macan') || 
        model.includes('Kuga') || model.includes('Mokka') || model.includes('Grandland')) {
      return 'SUV';
    }
    if (model.includes('Kombi') || model.includes('Touring') || model.includes('Avant')) {
      return 'Kombi';
    }
    if (model.includes('Cabrio') || model.includes('Roadster')) {
      return 'Cabrio';
    }
    if (model.includes('Coupe') || model.includes('911')) {
      return 'Coupe';
    }
    return 'Limousine';
  }

  // Generiere Fahrzeugausstattung
  generateFeatures(make, model) {
    const baseFeatures = ['ABS', 'ESP', 'Klimaanlage', 'Zentralverriegelung'];
    const premiumFeatures = ['Navigationssystem', 'Ledersitze', 'Sitzheizung', 'Xenon-Licht', 'Tempomat'];
    const luxuryFeatures = ['Massagesitze', 'Head-Up Display', 'Surround-Sound', 'Luftfederung'];
    
    let features = [...baseFeatures];
    
    if (make === 'BMW' || make === 'Mercedes-Benz' || make === 'Audi') {
      features.push(...premiumFeatures.slice(0, 3));
    }
    
    if (make === 'Porsche' || model.includes('S-Klasse') || model.includes('7er')) {
      features.push(...luxuryFeatures.slice(0, 2));
    }
    
    return features;
  }

  // Generiere Marktdaten
  generateMarketData(searchQuery) {
    const marketData = [];
    
    for (let i = 0; i < 15; i++) {
      marketData.push({
        title: `${searchQuery || 'BMW 320i'} - Marktvergleich ${i + 1}`,
        price: 35000 + Math.floor(Math.random() * 20000),
        mileage: Math.floor(Math.random() * 100000) + 10000,
        year: 2020 + Math.floor(Math.random() * 4),
        source: 'mobile.de'
      });
    }
    
    return marketData;
  }

  // Simuliere Netzwerkverzögerung
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Alle Fahrzeuge abrufen (simuliert Backend-API)
  async getAllVehicles(params = {}) {
    await this.delay(500);
    
    const vehicles = this.generateRealisticVehicles();
    
    return {
      success: true,
      data: vehicles,
      pagination: {
        page: 1,
        limit: 50,
        total: vehicles.length,
        totalPages: 1
      }
    };
  }

  // Einzelnes Fahrzeug abrufen
  async getVehicleById(id) {
    await this.delay(200);
    
    const vehicles = this.generateRealisticVehicles();
    const vehicle = vehicles.find(v => v.id === id);
    
    if (vehicle) {
      return {
        success: true,
        data: vehicle
      };
    } else {
      return {
        success: false,
        error: 'Fahrzeug nicht gefunden'
      };
    }
  }

  // Fahrzeug erstellen
  async createVehicle(vehicleData) {
    await this.delay(300);
    
    const newVehicle = {
      ...vehicleData,
      id: `new-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return {
      success: true,
      data: newVehicle
    };
  }

  // Fahrzeug aktualisieren
  async updateVehicle(id, updates) {
    await this.delay(300);
    
    return {
      success: true,
      data: {
        id,
        ...updates,
        updatedAt: new Date().toISOString()
      }
    };
  }

  // Fahrzeug löschen
  async deleteVehicle(id) {
    await this.delay(200);
    
    return {
      success: true,
      message: 'Fahrzeug erfolgreich gelöscht'
    };
  }

  // Marktpreisanalyse
  async getMarketAnalysis(make, model, year) {
    await this.delay(1000);
    
    const basePrice = 35000;
    const variance = 10000;
    const prices = Array.from({length: 10}, () => 
      basePrice + Math.floor(Math.random() * variance * 2) - variance
    );
    
    const averagePrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    return {
      success: true,
      data: {
        averagePrice: Math.round(averagePrice),
        priceRange: {
          min: Math.min(...prices),
          max: Math.max(...prices)
        },
        sampleSize: prices.length,
        recommendations: [
          'Preis liegt im Marktdurchschnitt',
          'Gute Verkaufschancen bei aktueller Preisgestaltung',
          'Markttrend: stabil'
        ]
      }
    };
  }
}

const mockBackendService = new MockBackendService();
export default mockBackendService;
