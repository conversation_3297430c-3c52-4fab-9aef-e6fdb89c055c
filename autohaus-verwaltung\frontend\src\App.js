import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import VehicleDetails from './pages/VehicleDetails';
import Auth from './pages/Auth';
import DragDropDashboard from './components/DragDropDashboard';
import './styles/App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/management" element={<DragDropDashboard />} />
          <Route path="/auth" element={<Auth />} />
          <Route path="/vehicle/:id" element={<VehicleDetails />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;