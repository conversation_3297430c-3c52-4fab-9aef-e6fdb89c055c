// Fahrzeug-Service für lokale Datenverwaltung

// Mock-Daten für Autohaus-Fahrzeuge
const mockVehicles = [
  // Beispiel-Fahrzeuge für dein Autohaus
  { id: 'vh-001', make: 'BMW', model: '320i', year: 2023, price: 45000, status: 'verfügbar', type: 'Limousine', fuel: 'Benzin', km: 12500, color: 'Schwarz', vin: 'WBA123456789', location: 'Stellplatz A1' },
  { id: 'vh-002', make: 'Mercedes', model: 'C-Klasse', year: 2022, price: 52000, status: 'verkauft', type: 'Limousine', fuel: 'Diesel', km: 25000, color: 'Silber', vin: 'WDD987654321', location: 'Stellplatz A2' },
  { id: 'vh-003', make: 'Audi', model: 'A4', year: 2023, price: 48000, status: 'reserviert', type: 'Limousine', fuel: 'Benzin', km: 8500, color: 'Weiß', vin: 'WAU456789123', location: 'Stellplatz B1' },
  { id: 'vh-004', make: 'Volkswagen', model: 'Golf', year: 2022, price: 28000, status: 'verfügbar', type: 'Kompakt', fuel: 'Benzin', km: 18000, color: 'Blau', vin: 'WVW789123456', location: 'Stellplatz B2' },
  { id: 'vh-005', make: 'Tesla', model: 'Model 3', year: 2023, price: 55000, status: 'verfügbar', type: 'Limousine', fuel: 'Elektro', km: 5000, color: 'Rot', vin: 'TES123456789', location: 'Stellplatz C1', smartcarConnected: true, battery: 85 }
];

// Generiere weitere Mock-Fahrzeuge bis 80
function generateMockVehicles() {
  const vehicles = [...mockVehicles];
  const makes = ['BMW', 'Mercedes', 'Audi', 'Volkswagen', 'Opel', 'Ford', 'Renault', 'Peugeot', 'Skoda', 'Seat'];
  const models = ['Limousine', 'Kombi', 'SUV', 'Kompakt', 'Cabrio'];
  const colors = ['Schwarz', 'Weiß', 'Silber', 'Blau', 'Rot', 'Grau', 'Grün'];
  const fuels = ['Benzin', 'Diesel', 'Elektro', 'Hybrid'];
  const statuses = ['verfügbar', 'verkauft', 'reserviert', 'werkstatt'];
  
  for (let i = 6; i <= 80; i++) {
    const make = makes[Math.floor(Math.random() * makes.length)];
    const model = models[Math.floor(Math.random() * models.length)];
    const year = 2020 + Math.floor(Math.random() * 4);
    const price = 20000 + Math.floor(Math.random() * 60000);
    const km = Math.floor(Math.random() * 100000);
    const color = colors[Math.floor(Math.random() * colors.length)];
    const fuel = fuels[Math.floor(Math.random() * fuels.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    vehicles.push({
      id: `vh-${i.toString().padStart(3, '0')}`,
      make,
      model: `${make} ${model}`,
      year,
      price,
      status,
      type: model,
      fuel,
      km,
      color,
      vin: `VIN${i.toString().padStart(9, '0')}`,
      location: `Stellplatz ${String.fromCharCode(65 + Math.floor(i/10))}${(i%10)+1}`,
      smartcarConnected: Math.random() < 0.1 // 10% haben Smartcar
    });
  }
  
  return vehicles;
}

class VehicleService {
  constructor() {
    this.vehicles = this.loadVehicles();
  }
  
  loadVehicles() {
    // Lade aus localStorage oder verwende Mock-Daten
    const stored = localStorage.getItem('autohaus_vehicles');
    if (stored) {
      return JSON.parse(stored);
    }
    const vehicles = generateMockVehicles();
    this.saveVehicles(vehicles);
    return vehicles;
  }
  
  saveVehicles(vehicles = this.vehicles) {
    localStorage.setItem('autohaus_vehicles', JSON.stringify(vehicles));
  }
  
  getAllVehicles() {
    return this.vehicles;
  }
  
  getVehicleById(id) {
    return this.vehicles.find(v => v.id === id);
  }
  
  getVehiclesByStatus(status) {
    return this.vehicles.filter(v => v.status === status);
  }
  
  searchVehicles(query) {
    const q = query.toLowerCase();
    return this.vehicles.filter(v => 
      v.make.toLowerCase().includes(q) ||
      v.model.toLowerCase().includes(q) ||
      v.color.toLowerCase().includes(q) ||
      v.vin.toLowerCase().includes(q)
    );
  }
  
  addVehicle(vehicle) {
    const newId = `vh-${(this.vehicles.length + 1).toString().padStart(3, '0')}`;
    const newVehicle = { ...vehicle, id: newId };
    this.vehicles.push(newVehicle);
    this.saveVehicles();
    return newVehicle;
  }
  
  updateVehicle(id, updates) {
    const index = this.vehicles.findIndex(v => v.id === id);
    if (index !== -1) {
      this.vehicles[index] = { ...this.vehicles[index], ...updates };
      this.saveVehicles();
      return this.vehicles[index];
    }
    return null;
  }
  
  deleteVehicle(id) {
    const index = this.vehicles.findIndex(v => v.id === id);
    if (index !== -1) {
      const deleted = this.vehicles.splice(index, 1)[0];
      this.saveVehicles();
      return deleted;
    }
    return null;
  }
  
  getStatistics() {
    const total = this.vehicles.length;
    const available = this.vehicles.filter(v => v.status === 'verfügbar').length;
    const sold = this.vehicles.filter(v => v.status === 'verkauft').length;
    const reserved = this.vehicles.filter(v => v.status === 'reserviert').length;
    const workshop = this.vehicles.filter(v => v.status === 'werkstatt').length;
    const smartcarConnected = this.vehicles.filter(v => v.smartcarConnected).length;
    
    return {
      total,
      available,
      sold,
      reserved,
      workshop,
      smartcarConnected
    };
  }
}

const vehicleService = new VehicleService();
export default vehicleService;